let category = '';
let selectedLearners = new Set();
let pathways = [];
let learningPathData = {};
let selectedCourses = new Map();
let totalCost = 0;
const COST_PER_COURSE = 30;

// Add this CSS at the beginning of the file where the other styles are defined
const modalStyles = document.createElement('style');
modalStyles.textContent = `
  .recommendation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .recommendation-modal {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .modal-radio-group {
    margin: 16px 0;
  }

  .modal-radio-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    cursor: pointer;
  }

  .modal-radio-item input[type="radio"] {
    margin-right: 8px;
  }

  .modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
  }

  .modal-buttons button {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s;
  }

  .modal-buttons button:first-child {
    color: #4B5563;
    background-color: white;
    border: 1px solid #D1D5DB;
  }

  .modal-buttons button:first-child:hover {
    background-color: #F3F4F6;
  }

  .modal-buttons button:last-child {
    color: white;
    background-color: #2563EB;
  }

  .modal-buttons button:last-child:hover {
    background-color: #1D4ED8;
  }
`;
document.head.appendChild(modalStyles);

// Add these styles for the disclaimer modal
const disclaimerStyles = document.createElement('style');
disclaimerStyles.textContent = `
  .disclaimer-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .disclaimer-modal {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(20px);
    transition: transform 0.3s ease-in-out;
  }

  .disclaimer-modal h3 {
    color: #2563EB;  /* Match the blue theme */
  }

  .disclaimer-modal-show {
    opacity: 1;
  }

  .disclaimer-modal-show .disclaimer-modal {
    transform: translateY(0);
  }

  .disclaimer-modal ul li::before {
    color: #2563EB;  /* Match the blue theme for list bullets */
  }
`;
document.head.appendChild(disclaimerStyles);

async function loadLearningPathData() {
  try {
    // Fetch digital skills, soft skills, and AI skills data in parallel
    const [digitalResponse, softSkillsResponse, aiSkillsResponse] = await Promise.all([
      fetch('learning-path-data.json'),
      fetch('learning-path-data_softskills.json'),
      fetch('learning-path-data_ai.json')
    ]);

    if (!digitalResponse.ok) {
      throw new Error(`HTTP error fetching digital skills data! status: ${digitalResponse.status}`);
    }
    if (!softSkillsResponse.ok) {
      throw new Error(`HTTP error fetching soft skills data! status: ${softSkillsResponse.status}`);
    }
    if (!aiSkillsResponse.ok) {
      throw new Error(`HTTP error fetching AI skills data! status: ${aiSkillsResponse.status}`);
    }

    const digitalData = await digitalResponse.json();
    const softSkillsData = await softSkillsResponse.json();
    const aiSkillsData = await aiSkillsResponse.json();

    // Merge the data from all three sources
    // For each pathway (essentials, intermediate, advanced, champions)
    const pathways = ['essentials', 'intermediate', 'advanced', 'champions'];

    learningPathData = {};

    pathways.forEach(pathway => {
      if (digitalData[pathway] && softSkillsData[pathway] && aiSkillsData[pathway]) {
        // Mark each course with its skill type
        digitalData[pathway].courseCategories.forEach(category => {
          category.courses.forEach(course => {
            course.skillType = 'digital';
          });
        });

        softSkillsData[pathway].courseCategories.forEach(category => {
          category.courses.forEach(course => {
            course.skillType = 'soft';
          });
        });

        aiSkillsData[pathway].courseCategories.forEach(category => {
          category.courses.forEach(course => {
            course.skillType = 'ai';
          });
        });

        // Combine the course categories
        learningPathData[pathway] = {
          title: digitalData[pathway].title,
          description: digitalData[pathway].description,
          softSkillsTitle: softSkillsData[pathway].title,
          softSkillsDescription: softSkillsData[pathway].description,
          aiSkillsTitle: aiSkillsData[pathway].title,
          aiSkillsDescription: aiSkillsData[pathway].description,
          courseCategories: [
            ...digitalData[pathway].courseCategories,
            ...softSkillsData[pathway].courseCategories,
            ...aiSkillsData[pathway].courseCategories
          ]
        };
      }
    });

    console.log('Combined learning path data loaded:', learningPathData);
  } catch (error) {
    console.error('Error loading learning path data:', error);
  }
}

function renderSkeletonLoaders() {
  const pathwaysContainer = document.getElementById('pathwaysContainer');
  pathwaysContainer.innerHTML = '';

  const skeletonPathwayCount = 4;
  for (let i = 0; i < skeletonPathwayCount; i++) {
    const skeletonPathway = document.createElement('div');
    skeletonPathway.classList.add('pathway-section', 'mb-6', 'animate-pulse');
    const skeletonHeader = document.createElement('div');
    skeletonHeader.classList.add(
      'flex',
      'items-center',
      'justify-between',
      'bg-white',
      'p-4',
      'rounded-t-md',
      'shadow-md'
    );
    skeletonHeader.innerHTML = `
      <div class="flex items-center">
        <div class="bg-gray-300 h-6 w-6 mr-4 rounded-full"></div>
        <div>
          <div class="bg-gray-300 h-4 w-32 mb-2 rounded"></div>
          <div class="bg-gray-200 h-3 w-48 rounded"></div>
        </div>
      </div>
    `;
    skeletonPathway.appendChild(skeletonHeader);

    // Skeleton for pathway content
    const skeletonContent = document.createElement('div');
    skeletonContent.classList.add(
      'bg-white',
      'rounded-b-md',
      'shadow-md',
      'overflow-hidden'
    );
    const skeletonLearnersGrid = document.createElement('div');
    skeletonLearnersGrid.classList.add(
      'grid',
      'grid-cols-1',
      'sm:grid-cols-2',
      'md:grid-cols-3',
      'lg:grid-cols-4',
      'gap-4',
      'p-4'
    );

    // Number of skeleton learner cards per pathway
    const skeletonLearnerCount = 8;
    for (let j = 0; j < skeletonLearnerCount; j++) {
      const skeletonLearnerCard = document.createElement('div');
      skeletonLearnerCard.classList.add(
        'bg-white',
        'rounded-lg',
        'shadow-md',
        'p-4',
        'flex',
        'items-center',
        'space-x-4'
      );

      skeletonLearnerCard.innerHTML = `
        <div class="flex-shrink-0">
          <div class="bg-gray-300 w-5 h-5 rounded"></div>
        </div>
        <div class="flex-grow flex flex-col items-center justify-center">
          <div class="bg-gray-300 h-4 w-24 mb-2 rounded"></div>
          <div class="bg-gray-200 h-3 w-16 rounded"></div>
        </div>
      `;

      skeletonLearnersGrid.appendChild(skeletonLearnerCard);
    }

    skeletonContent.appendChild(skeletonLearnersGrid);
    skeletonPathway.appendChild(skeletonContent);
    pathwaysContainer.appendChild(skeletonPathway);
  }
}

async function fetchPathwaysData(userCompany) {
  try {
    const companyRef = db.collection('companies').doc(userCompany);
    const userSnapshot = await companyRef.collection('users').get();
    const users = userSnapshot.docs;

    const pathwayData = {
      essentials: {
        name: 'Essentials',
        description: 'Core digital skills for office productivity and basic cybersecurity.',
        learners: [],
      },
      intermediate: {
        name: 'Intermediate',
        description: 'Advanced office techniques and enhanced collaboration skills.',
        learners: [],
      },
      advanced: {
        name: 'Advanced',
        description: 'Expert-level proficiency in Microsoft tools and cybersecurity.',
        learners: [],
      },
      champions: {
        name: 'Champions',
        description: 'Cutting-edge AI, collaboration, and security for top innovators.',
        learners: [],
      },
    };

    const userPromises = users.map(async (doc) => {
      const userData = doc.data();

      const [digitalResultsSnapshot, softSkillsResultsSnapshot] = await Promise.all([
        doc.ref.collection('assessmentResults')
          .orderBy('timestamp', 'desc')
          .limit(1)
          .get(),
        doc.ref.collection('softSkillsAssessmentResults')
          .orderBy('timestamp', 'desc')
          .limit(1)
          .get()
      ]);

      const digitalResult = !digitalResultsSnapshot.empty ? digitalResultsSnapshot.docs[0].data() : null;
      const softSkillsResult = !softSkillsResultsSnapshot.empty ? softSkillsResultsSnapshot.docs[0].data() : null;

      // Get paths directly
      const digitalPath = digitalResult?.section?.toLowerCase();
      const softSkillsPath = softSkillsResult?.section?.toLowerCase();

      // Function to add user to a pathway
      const addToPathway = (pathwayId, assessmentType) => {
        if (pathwayData[pathwayId]) {
          // Check if user already exists in this pathway
          const existingLearner = pathwayData[pathwayId].learners.find(l => l.id === doc.id);

          if (!existingLearner) {
            const enrolled = userData.enrollmentStatus === 'enrolled';
            pathwayData[pathwayId].learners.push({
              id: doc.id,
              name: `${userData.firstName} ${userData.lastName}`,
              email: userData.userEmail,
              enrolled: enrolled,
              enrollmentStatus: userData.enrollmentStatus || null,
              enrolledLearningPath: userData.enrolledLearningPath || null,
              assessmentInfo: {
                digital: digitalResult ? {
                  section: digitalResult.section,
                  timestamp: digitalResult.timestamp
                } : null,
                softSkills: softSkillsResult ? {
                  section: softSkillsResult.section,
                  timestamp: softSkillsResult.timestamp
                } : null
              },
              primaryAssessmentType: assessmentType // Track which assessment put them in this path
            });
          }
        }
      };

      // Add user to pathways based on both assessment types
      if (digitalPath) {
        addToPathway(digitalPath, 'digital');
      }
      if (softSkillsPath) {
        addToPathway(softSkillsPath, 'softSkills');
      }
    });

    await Promise.all(userPromises);

    pathways = Object.entries(pathwayData).map(([id, data]) => ({
      id,
      name: data.name,
      description: data.description,
      icon: getPathwayIcon(id),
      learners: data.learners,
    }));
  } catch (error) {
    console.error('Error fetching pathways data:', error);
  }
}

function getPathwayIcon(pathwayId) {
  const icons = {
      essentials: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>`,
      intermediate: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>`,
      advanced: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>`,
      champions: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
      </svg>`
  };
  return icons[pathwayId] || '';
}

// Function to render all pathways
function renderPathways(showAll = false) {
  const pathwaysContainer = document.getElementById('pathwaysContainer');
  pathwaysContainer.innerHTML = '';

  // Sort pathways to ensure the current category appears first
  const sortedPathways = [...pathways].sort((a, b) => {
    if (a.id === category) return -1;
    if (b.id === category) return 1;
    return 0;
  });

  sortedPathways.forEach((pathway, index) => {
    if (!pathway.learners || pathway.learners.length === 0) return;
    if (!showAll && pathway.id !== category) return;

    // Check if all learners in this pathway are enrolled or processing
    const allEnrolledOrProcessing = pathway.learners.every(learner =>
      learner.enrolled || learner.enrollmentStatus === 'processing'
    );

    // If all learners are enrolled or processing, skip rendering this pathway
    if (allEnrolledOrProcessing) return;

    // Sort learners: enrolled and processing entries appear first
    pathway.learners.sort((a, b) => {
      if (a.enrolled === b.enrolled) {
        if (a.enrollmentStatus === 'processing' && b.enrollmentStatus !== 'processing') return -1;
        if (b.enrollmentStatus === 'processing' && a.enrollmentStatus !== 'processing') return 1;
        return 0;
      }
      return a.enrolled ? -1 : 1;
    });

    // Create pathway section
    const pathwaySection = document.createElement('div');
    pathwaySection.classList.add('pathway-section', 'mb-6', 'opacity-0', 'transition-opacity', 'duration-500');

    // Pathway header
    const pathwayHeader = document.createElement('div');
    pathwayHeader.classList.add('flex', 'items-center', 'justify-between', 'bg-white', 'p-4', 'rounded-t-md', 'shadow-md', 'pathway-header');

    pathwayHeader.innerHTML = `
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div class="flex items-center mb-4 sm:mb-0">
        <div class="text-blue-800 mr-4">
          ${pathway.icon}
        </div>
        <div>
          <h2 class="text-xl font-semibold text-blue-800">${pathway.name}</h2>
          <p class="text-gray-600 mr-4">${pathway.description}</p>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 sm:ml-4">
        <!-- Enroll All button with Info Icon -->
        <div class="flex items-center">
          <span onclick="enrollAll('${pathway.id}')"
                class="enroll-all-button text-blue-600 hover:text-blue-800 cursor-pointer transition-colors duration-200 text-sm font-medium">
            Auto-enroll all
          </span>
          <!-- Info Icon with Tooltip -->
          <div class="relative ml-2 tooltip-container">
            <svg class="w-5 h-5 text-gray-500 hover:text-gray-700 cursor-pointer" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <title>Info</title>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 18.5a6.5 6.5 0 100-13 6.5 6.5 0 000 13z" />
            </svg>
            <!-- Tooltip -->
            <div class="tooltip">
              This will automatically enroll all eligible learners in their AI recommended courses based on their assessment results. Only learners with course recommendations will be enrolled. Each course costs £30 per learner.
            </div>
          </div>
        </div>
        <!-- Existing buttons -->
        <button onclick="toggleEnrolledVisibility('${pathway.id}')" class="toggle-enrolled-visibility flex items-center justify-center text-gray-600 hover:text-gray-800 w-full sm:w-auto">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
          </svg>
          <span class="toggle-text">Hide Enrolled</span>
        </button>
        <button onclick="toggleSection('${pathway.id}Section')" class="focus:outline-none w-full sm:w-auto flex items-center justify-center">
          <svg id="${pathway.id}-toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>
    </div>
  `;




    // Pathway content
    const pathwayContent = document.createElement('div');
    pathwayContent.id = `${pathway.id}Section`;
    pathwayContent.classList.add('bg-white', 'rounded-b-md', 'shadow-md', 'overflow-hidden');

    const learnersGrid = document.createElement('div');
    learnersGrid.classList.add('grid', 'grid-cols-1', 'sm:grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-4', 'gap-4', 'p-4', 'learners-grid');

    pathway.learners.forEach((learner, index) => {
      // Preserve the existing state for processing learners
      if (learner.enrollmentStatus === 'processing') {
        selectedLearners.add(learner.name);
      }
      const learnerCard = createLearnerCard(learner, pathway.id, index);
      learnersGrid.appendChild(learnerCard);
    });

    pathwayContent.appendChild(learnersGrid);

    pathwaySection.appendChild(pathwayHeader);
    pathwaySection.appendChild(pathwayContent);

    pathwaysContainer.appendChild(pathwaySection);

    // Trigger reflow to ensure the opacity transition works
    pathwaySection.offsetHeight;

    // Add fade-in effect with a slight delay for each pathway
    setTimeout(() => {
      pathwaySection.classList.remove('opacity-0');
    }, index * 100);
  });

  if (!showAll) {
    renderOtherPathwaysPrompt();
  }

  // Update total cost after rendering
  updateTotalCost();
}

function renderOtherPathwaysPrompt() {
  if (hasUnenrolledLearnersInOtherPathways(category)) {
    const promptDiv = document.createElement('div');
    promptDiv.classList.add('mt-6', 'p-4', 'bg-blue-50', 'rounded-md', 'shadow-sm');
    promptDiv.innerHTML = `
      <p class="text-blue-800">You have other learners not enrolled in other pathways. Do you wish to enroll them now?
        <span id="enrollOthersBtn" class="ml-2 text-blue-600 font-semibold cursor-pointer hover:underline">Enroll</span>
      </p>
    `;

    const pathwaysContainer = document.getElementById('pathwaysContainer');
    pathwaysContainer.appendChild(promptDiv);

    document.getElementById('enrollOthersBtn').addEventListener('click', () => {
      renderPathways(true);
      updateTotalCost();
    });
  }
}

function createLearnerCard(learner, pathwayId, index) {
  const card = document.createElement('div');
  card.classList.add('learner-card', 'bg-white', 'rounded-lg', 'shadow-md', 'p-4', 'flex', 'items-center', 'space-x-4', 'transition-colors', 'duration-200');
  card.classList.add(learner.enrolled ? 'enrolled' : learner.enrollmentStatus === 'processing' ? 'processing' : 'not-enrolled');
  card.setAttribute('data-learner-name', learner.name);
  card.setAttribute('data-learner-id', learner.id);
  card.setAttribute('data-enrolled', learner.enrolled);

  // Initialize recommendations data attribute as false
  card.setAttribute('data-has-recommendations', 'false');

  // Add company information to learner object
  learner.company = userCompany;

  // Load recommendations asynchronously and update the attribute
  loadRecommendations(learner).then(() => {
      const hasRecommendations = courseRecommendations.length > 0;
      card.setAttribute('data-has-recommendations', hasRecommendations.toString());
  });

  let statusText, statusClass, isCheckable;
  if (learner.enrolled) {
    statusText = 'Enrolled';
    statusClass = 'text-green-600';
    isCheckable = false;
  } else if (learner.enrollmentStatus === 'processing') {
    statusText = 'Processing';
    statusClass = 'text-yellow-600';
    isCheckable = false;
  } else {
    statusText = 'Not Enrolled';
    statusClass = 'text-gray-500';
    isCheckable = true;
  }

  const isSelected = selectedLearners.has(learner.name);

  // Modify the assessment badges generation
  const assessmentBadges = [];
  if (learner.assessmentInfo) {
    // Add badge only if this assessment type matches the learner's path
    if (learner.assessmentInfo.digital && learner.assessmentInfo.digital.section.toLowerCase() === pathwayId) {
      assessmentBadges.push(`
        <span class="inline-flex items-center justify-center w-5 h-5
          text-blue-600 hover:text-blue-700 transition-colors duration-200 group relative"
          title="Digital Skills Assessment">
          <svg class="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <span class="absolute bottom-full mb-1 hidden group-hover:block w-28 bg-gray-900 text-white text-[10px] px-2 py-1 rounded-md text-center -ml-14">
            Digital Skills
          </span>
        </span>`);
    }
    if (learner.assessmentInfo.softSkills && learner.assessmentInfo.softSkills.section.toLowerCase() === pathwayId) {
      assessmentBadges.push(`
        <span class="inline-flex items-center justify-center w-5 h-5
          text-purple-600 hover:text-purple-700 transition-colors duration-200 group relative"
          title="Soft Skills Assessment">
          <svg class="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <span class="absolute bottom-full mb-1 hidden group-hover:block w-24 bg-gray-900 text-white text-[10px] px-2 py-1 rounded-md text-center -ml-12">
            Soft Skills
          </span>
        </span>`);
    }
  }

  card.innerHTML = `
    <div class="flex-shrink-0">
      <input type="checkbox" class="enroll-checkbox w-5 h-5 text-blue-600"
        ${!isCheckable ? 'checked disabled' : ''}
        ${isSelected ? 'checked' : ''}>
    </div>
    <div class="flex-grow flex flex-col items-center justify-center">
      <h3 class="learner-name text-base font-medium text-gray-700">${learner.name}</h3>
      <p class="learner-status text-sm ${statusClass}">${statusText}</p>
      <div class="flex gap-1 mt-1">
        ${assessmentBadges.join('')}
      </div>
    </div>
  `;

  const checkbox = card.querySelector('.enroll-checkbox');
  if (isCheckable) {
    checkbox.addEventListener('change', () => {
      if (checkbox.checked) {
        updateEnrollment(checkbox, learner, pathwayId);
        showCourseSelectionOverlay(learner, pathwayId);
      } else {
        updateEnrollment(checkbox, learner, pathwayId);
        selectedCourses.delete(learner.name);
        updateTotalCost();
      }
    });
  }

  return card;
}

function showCourseSelectionOverlay(learner, pathwayId) {
  // Remove loading animation when course selection overlay is displayed
  const learnerCard = document.querySelector(`.learner-card[data-learner-name="${learner.name}"]`);

  // Create the course selection overlay and wait for it to be ready
  createCourseSelectionOverlay(learner, pathwayId).then(() => {
    if (learnerCard) {
      // Remove the loading animation class once the overlay is ready
      learnerCard.classList.remove('learner-card-loading');
      const statusElement = learnerCard.querySelector('.learner-status');
      statusElement.textContent = 'Pending';
      statusElement.classList.remove('text-blue-600');
      statusElement.classList.add('text-orange-600');
    }
  });
}

async function enrollAll(pathwayId) {
  const pathwayContent = document.getElementById(`${pathwayId}Section`);
  const enrollAllButton = pathwayContent.parentElement.querySelector('.enroll-all-button');
  const checkboxes = pathwayContent.querySelectorAll('.enroll-checkbox:not(:disabled)');

  // Determine current mode based on button text
  const isEnrolling = enrollAllButton.textContent.includes('Auto-enroll');

  if (isEnrolling) {
    // Show the recommendation selection modal
    const modalOverlay = document.createElement('div');
    modalOverlay.classList.add('recommendation-modal-overlay');

    const modal = document.createElement('div');
    modal.classList.add('recommendation-modal');

    modal.innerHTML = `
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Select Enrollment Type</h3>
      <p class="text-sm text-gray-600 mb-6">Choose whether to enroll learners in core recommended courses only or include follow-up course recommendations as well.</p>
      <div class="modal-radio-group">
        <label class="modal-radio-item">
          <input type="radio" name="enrollmentChoice" value="core" checked />
          <span class="text-gray-700">Core Recommendations Only</span>
        </label>
        <label class="modal-radio-item">
          <input type="radio" name="enrollmentChoice" value="coreOther" />
          <span class="text-gray-700">Core + Follow-Up Course Recommendations (Builds on core knowledge)</span>
        </label>
      </div>
      <div class="modal-buttons">
        <button id="cancelEnrollment" class="cancel-button">
          Cancel
        </button>
        <button id="confirmEnrollment" class="confirm-button">
          Continue
        </button>
      </div>
    `;

    modalOverlay.appendChild(modal);
    document.body.appendChild(modalOverlay);

    // Handle modal actions
    return new Promise((resolve) => {
      document.getElementById('cancelEnrollment').onclick = () => {
        modalOverlay.remove();
        resolve(false);
      };

      document.getElementById('confirmEnrollment').onclick = async () => {
        const choice = modal.querySelector('input[name="enrollmentChoice"]:checked').value;
        modalOverlay.remove();

        // Show loading overlay before starting the process
        showLoadingOverlay();

        try {
          let hasAnyRecommendations = false;
          let hasEnrolledAny = false;

          // Process each learner
          for (const checkbox of checkboxes) {
            const card = checkbox.closest('.learner-card');
            const learnerId = card.getAttribute('data-learner-id');
            const learner = {
              id: learnerId,
              name: card.getAttribute('data-learner-name'),
              enrolled: card.classList.contains('enrolled'),
              enrollmentStatus: card.classList.contains('processing') ? 'processing' : null,
              company: userCompany
            };

            // Wait for recommendations to load
            await Promise.all([
              loadRecommendations(learner),
              loadOtherPathRecommendations(learner)
            ]);

            const coreRecommendations = courseRecommendations.map(rec => rec.course);
            let allRecommendations = coreRecommendations;

            if (choice === 'coreOther') {
              const otherPathRecs = otherPathRecommendations.map(rec => rec.courseName);
              allRecommendations = [...coreRecommendations, ...otherPathRecs];
            }

            if (allRecommendations.length > 0 &&
                !learner.enrolled &&
                learner.enrollmentStatus !== 'processing') {
              hasAnyRecommendations = true;

              checkbox.checked = true;
              await updateEnrollment(checkbox, learner, pathwayId);

              selectedCourses.set(learner.name, allRecommendations);
              hasEnrolledAny = true;
            }
          }

          // Update button state based on enrollment results
          if (hasEnrolledAny) {
            enrollAllButton.textContent = 'Unenroll All';
            enrollAllButton.disabled = false;
            enrollAllButton.classList.remove('opacity-50', 'cursor-not-allowed');
          } else {
            enrollAllButton.disabled = true;
            enrollAllButton.classList.add('opacity-50', 'cursor-not-allowed');
            if (!hasAnyRecommendations) {
              enrollAllButton.title = 'No recommendations available for learners';
            }
          }

          resolve(true);
        } catch (error) {
          console.error('Error during auto-enrollment:', error);
          showNotification('An error occurred during auto-enrollment. Please try again.', 'error');
          resolve(false);
        } finally {
          hideLoadingOverlay();
        }
      };
    });
  } else {
    // Unenroll functionality remains the same
    showLoadingOverlay();
    try {
      let hasUnenrolledAny = false;

      for (const checkbox of checkboxes) {
        const card = checkbox.closest('.learner-card');
        const learner = {
          name: card.getAttribute('data-learner-name'),
          enrolled: card.classList.contains('enrolled'),
          enrollmentStatus: card.classList.contains('processing') ? 'processing' : null
        };

        if (!learner.enrolled &&
            learner.enrollmentStatus !== 'processing' &&
            checkbox.checked) {
          checkbox.checked = false;
          await updateEnrollment(checkbox, learner, pathwayId);
          selectedCourses.delete(learner.name);
          hasUnenrolledAny = true;
        }
      }

      if (hasUnenrolledAny) {
        await updateEnrollAllButtonState(pathwayId);
      }
    } catch (error) {
      console.error('Error during unenrollment:', error);
      showNotification('An error occurred during unenrollment. Please try again.', 'error');
    } finally {
      hideLoadingOverlay();
    }
  }

  updateTotalCost();
}

async function updateEnrollAllButtonState(pathwayId) {
  const pathwayContent = document.getElementById(`${pathwayId}Section`);
  const enrollAllButton = pathwayContent.parentElement.querySelector('.enroll-all-button');
  const checkboxes = pathwayContent.querySelectorAll('.enroll-checkbox:not(:disabled)');

  // Check for any selected learners
  const hasSelected = Array.from(checkboxes).some(checkbox => checkbox.checked);

  // Check for available recommendations
  let hasRecommendations = false;
  for (const checkbox of checkboxes) {
      const card = checkbox.closest('.learner-card');
      const learnerId = card.getAttribute('data-learner-id');
      const learner = pathways.flatMap(p => p.learners).find(l => l.id === learnerId);

      if (learner && !learner.enrolled && learner.enrollmentStatus !== 'processing') {
          await loadRecommendations(learner);
          if (courseRecommendations.length > 0) {
              hasRecommendations = true;
              break;
          }
      }
  }

  // Update button state
  if (hasSelected) {
      enrollAllButton.textContent = 'Unenroll All';
      enrollAllButton.classList.remove('opacity-50', 'pointer-events-none', 'text-gray-400');
      enrollAllButton.classList.add('text-blue-600', 'hover:text-blue-800');
      enrollAllButton.title = '';
  } else {
      enrollAllButton.textContent = 'Auto-enroll all';

      if (hasRecommendations) {
          enrollAllButton.classList.remove('opacity-50', 'pointer-events-none', 'text-gray-400');
          enrollAllButton.classList.add('text-blue-600', 'hover:text-blue-800');
          enrollAllButton.title = '';
      } else {
          enrollAllButton.classList.remove('text-blue-600', 'hover:text-blue-800');
          enrollAllButton.classList.add('opacity-50', 'pointer-events-none', 'text-gray-400');
          enrollAllButton.title = 'No recommendations available for learners in this pathway';
      }
  }
}



function unenrollAll(pathwayId) {
  const pathwayContent = document.getElementById(`${pathwayId}Section`);
  const checkboxes = pathwayContent.querySelectorAll('.enroll-checkbox:checked:not(:disabled)');
  const enrollAllButton = pathwayContent.parentElement.querySelector('.enroll-all-button');

  checkboxes.forEach((checkbox) => {
      const card = checkbox.closest('.learner-card');
      const learner = {
          name: card.getAttribute('data-learner-name'),
          enrolled: card.classList.contains('enrolled'),
          enrollmentStatus: card.classList.contains('processing') ? 'processing' : null
      };

      if (!learner.enrolled && learner.enrollmentStatus !== 'processing') {
          checkbox.checked = false;
          updateEnrollment(checkbox, learner, pathwayId);
          selectedCourses.delete(learner.name);
      }
  });

  enrollAllButton.textContent = 'Enroll All';
  updateEnrollAllButtonState(pathwayId);
}

function hasSelectedLearners(pathwayId) {
  const pathwayContent = document.getElementById(`${pathwayId}Section`);
  const checkboxes = pathwayContent.querySelectorAll('.enroll-checkbox:not(:disabled)');
  return Array.from(checkboxes).some(checkbox => checkbox.checked);
}



function updateEnrollment(checkbox, learner, pathwayId) {
  const card = checkbox.closest('.learner-card');
  const statusElement = card.querySelector('.learner-status');

  if (learner.enrolled || learner.enrollmentStatus === 'processing' || checkbox.disabled) {
    checkbox.checked = true;
    checkbox.disabled = true;
    return;
  }

  if (checkbox.checked) {
    // Apply loading animation to the specific card
    card.classList.remove('not-enrolled');
    card.classList.add('pending', 'learner-card-loading');
    statusElement.textContent = 'Loading...';
    statusElement.classList.remove('text-gray-500');
    statusElement.classList.add('text-blue-600');
    selectedLearners.add(learner.name);
  } else {
    card.classList.remove('pending', 'learner-card-loading');
    card.classList.add('not-enrolled');
    statusElement.textContent = 'Not Enrolled';
    statusElement.classList.remove('text-blue-600', 'text-orange-600');
    statusElement.classList.add('text-gray-500');
    selectedLearners.delete(learner.name);
  }

  updateTotalCost();
  updatePaymentButtonState();
  updateEnrollAllButtonState(pathwayId);
}

function updateEnrollAllButtonText(pathwayId) {
  const pathwayContent = document.getElementById(`${pathwayId}Section`);
  const enrollAllButton = pathwayContent.parentElement.querySelector('.enroll-all-button');
  const checkboxes = pathwayContent.querySelectorAll('.enroll-checkbox:not(:disabled)');
  const allChecked = Array.from(checkboxes).every(cb => cb.checked);

  enrollAllButton.textContent = allChecked ? 'Unenroll All' : 'Enroll All';
}


function toggleSection(sectionId) {
  const content = document.getElementById(sectionId);
  const icon = document.getElementById(`${sectionId.replace('Section', '')}-toggle-icon`);
  if (content.style.display === 'none') {
    content.style.display = 'block';
    icon.classList.remove('rotate-180');
  } else {
    content.style.display = 'none';
    icon.classList.add('rotate-180');
  }
}


function toggleEnrolledVisibility(pathwayId) {
  const pathwayContent = document.getElementById(`${pathwayId}Section`);
  const enrolledCards = pathwayContent.querySelectorAll('.learner-card[data-enrolled="true"]');
  const toggleButton = pathwayContent.parentElement.querySelector('.toggle-enrolled-visibility');
  const toggleText = toggleButton.querySelector('.toggle-text');
  const eyeIcon = toggleButton.querySelector('svg');

  let isHidden = toggleText.textContent === 'Show Enrolled';

  enrolledCards.forEach((card) => {
    card.style.display = isHidden ? 'flex' : 'none';
  });

  toggleText.textContent = isHidden ? 'Hide Enrolled' : 'Show Enrolled';

  // Update the eye icon
  if (isHidden) {
    eyeIcon.innerHTML = '<path d="M10 12a2 2 0 100-4 2 2 0 000 4z" /><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />';
  } else {
    eyeIcon.innerHTML = '<path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd" /><path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />';
  }
}

function updateTotalCost() {
  let totalCost = 0;

  Array.from(selectedLearners).forEach(learnerName => {
    const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
    if (learner && learner.enrollmentStatus !== 'processing' && !learner.enrolled) {
      const learnerCourses = selectedCourses.get(learnerName) || [];
      totalCost += learnerCourses.length * COST_PER_COURSE;
    }
  });

  const totalCostElement = document.getElementById('totalCost');
  totalCostElement.textContent = totalCost;

  updateOrderSummary();
}


function updateOrderSummary() {

  const newEnrollments = Array.from(selectedLearners).filter(learnerName => {
    const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
    return learner && learner.enrollmentStatus !== 'processing' && !learner.enrolled;
  });

  const totalCourses = Array.from(selectedCourses.entries())
    .filter(([learnerName]) => newEnrollments.includes(learnerName))
    .reduce((total, [_, courses]) => total + courses.length, 0);

  const orderSummaryElement = document.getElementById('orderSummary');

  if (totalCourses === 0) {
    orderSummaryElement.textContent = 'You haven\'t selected any courses for enrollment yet.';
  } else {
    const learnerSummary = newEnrollments.map(learnerName => {
      const courses = selectedCourses.get(learnerName) || [];
      return `${learnerName}: ${courses.length} course${courses.length !== 1 ? 's' : ''} (£${courses.length * COST_PER_COURSE})`;
    }).join(', ');

    orderSummaryElement.innerHTML = `
      You have selected courses for ${newEnrollments.length} learner${newEnrollments.length !== 1 ? 's' : ''}:
      <div class="mt-2 mb-2 text-sm leading-relaxed">
        ${learnerSummary}
      </div>
      Total courses: ${totalCourses} at £${COST_PER_COURSE} per course.
      <br><br>
      <strong>Note that you won't be charged immediately after clicking
      the confirm button. However, you will receive an invoice after further processing.</strong>
    `;
  }
}


function filterLearners() {
  const input = document.getElementById('searchInput');
  const filter = input.value.toLowerCase();
  const learnerCards = document.querySelectorAll('.learner-card');

  learnerCards.forEach((card) => {
    const name = card.getAttribute('data-learner-name').toLowerCase();
    if (name.includes(filter)) {
      card.style.display = 'block';
    } else {
      card.style.display = 'none';
    }
  });

  // Hide pathway sections with no visible learners
  const pathwaySections = document.querySelectorAll('.pathway-section');
  pathwaySections.forEach((section) => {
    const learners = section.querySelectorAll('.learner-card');
    let hasVisibleLearners = false;
    learners.forEach((learner) => {
      if (learner.style.display !== 'none') {
        hasVisibleLearners = true;
      }
    });
    if (hasVisibleLearners) {
      section.style.display = 'block';
    } else {
      section.style.display = 'none';
    }
  });
}

function setupBackNavigation() {
  const backButton = document.getElementById('backToDetail');
  if (backButton) {
      backButton.addEventListener('click', handleBackToDetail);
  }
}

function handleBackToDetail() {
  console.log('Handling back to detail');
  navigateBack();
}

function resetSelections() {
  selectedLearners.clear();
  const checkboxes = document.querySelectorAll('.enroll-checkbox:not(:disabled)');
  checkboxes.forEach(checkbox => {
    checkbox.checked = false;
    const card = checkbox.closest('.learner-card');
    card.classList.remove('pending', 'bg-yellow-50');
    card.classList.add('not-enrolled');
    const statusElement = card.querySelector('.learner-status');
    statusElement.textContent = 'Not Enrolled';
    statusElement.classList.remove('text-yellow-600');
    statusElement.classList.add('text-gray-500');
  });
  updateTotalCost();
  updatePaymentButtonState();
}

function setupConfirmOrderButton() {
  const confirmOrderButton = document.getElementById('proceedToPayment');
  if (confirmOrderButton) {
    confirmOrderButton.addEventListener('click', proceedToPayment);
  } else {
    console.error('Confirm Order button not found');
  }
}

async function initializeEnrollment(passedCategory, passedUserCompany, params = {}) {
  console.log('initializeEnrollment called with:', { passedCategory, passedUserCompany, params });
  await loadLearningPathData();

  if (!passedCategory) {
      console.error('Missing required parameter: passedCategory');
      passedCategory = 'default';
  }

  if (!passedUserCompany) {
      console.error('Missing required parameter: passedUserCompany');
      return;
  }

  // Ensure we use the right company name when in demo mode
  if (window.isDemoMode) {
      userCompany = 'Barefoot eLearning';
      console.log('Enrollment page using demo company:', userCompany);
  } else {
      userCompany = passedUserCompany;
      console.log('Enrollment page using company:', userCompany);
  }

  category = passedCategory.toLowerCase();

  console.log('Proceeding with category:', category);
  pushToNavigationState('enrollment', category, params);

  resetSelections();
  renderSkeletonLoaders();
  fetchPathwaysData(userCompany).then(() => {
      console.log('Pathways data fetched:', pathways);
      renderPathways();

      // Check if we need to highlight a user
      if (params.highlightUser) {
          try {
              const userData = JSON.parse(atob(params.highlightUser));
              highlightLearnerCard(userData.email);
          } catch (error) {
              console.error('Error processing user highlight data:', error);
          }
      }

      updateTotalCost();
      setupBackNavigation(category);
      setupEnrollmentStatusListener(userCompany);
      setupConfirmOrderButton();
      updateConfirmButtonState();
  }).catch(error => {
      console.error('Error fetching pathways data:', error);
  });

  // Add this after the existing initialization code
  await showEnrollmentDisclaimer();
}

function highlightLearnerCard(userEmail) {
  // Find the learner card with matching email
  const learnerCard = document.querySelector(`.learner-card[data-learner-id="${userEmail}"]`);
  if (learnerCard) {
      // Add highlight class
      learnerCard.classList.add('highlighted-learner');

      // Scroll the card into view with smooth behavior
      learnerCard.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Remove highlight after animation completes
      setTimeout(() => {
          learnerCard.classList.remove('highlighted-learner');
      }, 3000); // 3 seconds total (matches animation duration)
  }
}

// Add these styles at the beginning of the file
const highlightStyles = document.createElement('style');
highlightStyles.textContent = `
  @keyframes highlightPulse {
      0% {
          box-shadow: 0 0 0 0 rgba(30, 58, 138, 0.4);
          border-color: #1e3a8a;
      }
      70% {
          box-shadow: 0 0 0 10px rgba(30, 58, 138, 0);
          border-color: #1e3a8a;
      }
      100% {
          box-shadow: 0 0 0 0 rgba(30, 58, 138, 0);
          border-color: initial;
      }
  }

  .highlighted-learner {
      animation: highlightPulse 1s ease-in-out 3;
      border: 2px solid #1e3a8a;
      transition: all 0.3s ease-in-out;
  }
`;
document.head.appendChild(highlightStyles);


async function sendOrderConfirmation(enrollments, cost, selectedCourses) {
  const orderDetails = {
    userCompany,
    enrollments: enrollments.map(learnerName => {
      const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
      return {
        name: learnerName,
        email: learner.email,
        section: learner.enrolledLearningPath || pathways.find(p => p.learners.includes(learner)).id,
        courses: selectedCourses.get(learnerName) || []
      };
    }),
    totalCost: cost // Pass the numeric cost value
  };

  try {
    const response = await fetch('/send-order-confirmation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderDetails),
    });

    if (!response.ok) {
      throw new Error('Failed to send order confirmation email');
    }

    console.log('Order confirmation email sent successfully');
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
  }
}


async function proceedToPayment() {
  const paymentButton = document.getElementById('proceedToPayment');
  const buttonText = paymentButton.querySelector('.button-text');
  const processingText = paymentButton.querySelector('.processing-text');

  // Disable button and show processing state
  paymentButton.disabled = true;
  paymentButton.classList.add('opacity-75', 'cursor-not-allowed');
  buttonText.classList.add('hidden');
  processingText.classList.remove('hidden');

  const batch = db.batch();
  const companyRef = db.collection('companies').doc(userCompany);

  const newEnrollments = Array.from(selectedLearners).filter(learnerName => {
    const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
    return learner && learner.enrollmentStatus !== 'processing' && !learner.enrolled;
  });

  try {
    for (const learnerName of newEnrollments) {
      const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
      if (learner) {
        const userRef = companyRef.collection('users').doc(learner.id);
        const selectedCoursesForLearner = selectedCourses.get(learnerName) || [];
        batch.update(userRef, {
          enrollmentStatus: 'processing',
          enrolledLearningPath: learner.enrolledLearningPath || pathways.find(p => p.learners.includes(learner)).id,
          enrolledCourses: selectedCoursesForLearner
        });
      }
    }

    await batch.commit();
    const totalCost = Array.from(selectedCourses.entries())
      .filter(([learnerName]) => newEnrollments.includes(learnerName))
      .reduce((total, [_, courses]) => total + (courses.length * COST_PER_COURSE), 0);

    await sendOrderConfirmation(newEnrollments, totalCost, selectedCourses);

    // Track enrollment completion milestone
    trackMilestone('enrollment_completed', {
      enrolledLearners: newEnrollments.length,
      totalCost: totalCost,
      totalCourses: Array.from(selectedCourses.entries())
        .filter(([learnerName]) => newEnrollments.includes(learnerName))
        .reduce((total, [_, courses]) => total + courses.length, 0),
      category: category,
      userCompany: userCompany
    });

    // Update pathways data
    newEnrollments.forEach(learnerName => {
      const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
      if (learner) {
        learner.enrollmentStatus = 'processing';
        learner.enrolled = false;
      }
    });

    // Update UI for newly enrolled learners
    newEnrollments.forEach(learnerName => {
      const learnerCard = document.querySelector(`.learner-card[data-learner-name="${learnerName}"]`);
      if (learnerCard) {
        const statusElement = learnerCard.querySelector('.learner-status');
        const checkbox = learnerCard.querySelector('.enroll-checkbox');

        learnerCard.classList.remove('not-enrolled', 'pending', 'bg-orange-50');
        learnerCard.classList.add('processing', 'bg-yellow-50');
        statusElement.textContent = 'Processing';
        statusElement.classList.remove('text-gray-500', 'text-orange-600');
        statusElement.classList.add('text-yellow-600');
        checkbox.checked = true;
        checkbox.disabled = true;
      }
    });

    selectedLearners.clear();
    selectedCourses.clear();
    updateTotalCost();

    const totalCourses = Array.from(selectedCourses.values()).reduce((acc, courses) => acc + courses.length, 0);
    showNotification(`We have received your order to enroll ${newEnrollments.length} learner${newEnrollments.length !== 1 ? 's' : ''} in ${totalCourses} course${totalCourses !== 1 ? 's' : ''}. You will receive an email with further details and an invoice. Thank you!`);

  } catch (error) {
    console.error('Error updating enrollment status:', error);
    showNotification('An error occurred while processing your order. Please try again.', 'error');

    // Revert button state on error
    paymentButton.disabled = false;
    paymentButton.classList.remove('opacity-75', 'cursor-not-allowed');
    buttonText.classList.remove('hidden');
    processingText.classList.add('hidden');
  }

  // Revert button state after processing (whether successful or not)
  setTimeout(() => {
    paymentButton.disabled = false;
    paymentButton.classList.remove('opacity-75', 'cursor-not-allowed');
    buttonText.classList.remove('hidden');
    processingText.classList.add('hidden');
  }, 3000);
}

function updatePaymentButtonState() {
  const paymentButton = document.getElementById('proceedToPayment');
  if (!paymentButton) return;

  // Filter out learners who are already enrolled or processing
  const newEnrollments = Array.from(selectedLearners).filter(learnerName => {
    const learner = pathways.flatMap(p => p.learners).find(l => l.name === learnerName);
    return learner && learner.enrollmentStatus !== 'processing' && !learner.enrolled;
  });

  // Update button state based on whether there are any new enrollments
  const hasNewEnrollments = newEnrollments.length > 0;
  paymentButton.disabled = !hasNewEnrollments;

  // Update button appearance
  if (hasNewEnrollments) {
    paymentButton.classList.remove('opacity-50', 'cursor-not-allowed');
    paymentButton.classList.add('hover:bg-blue-700');
  } else {
    paymentButton.classList.add('opacity-50', 'cursor-not-allowed');
    paymentButton.classList.remove('hover:bg-blue-700');
  }
}


function showNotification(message, type = 'success') {
  const notificationContainer = document.createElement('div');
  notificationContainer.classList.add('fixed', 'top-4', 'right-4', 'z-50', 'p-4', 'rounded-md', 'shadow-md', 'max-w-md');

  if (type === 'success') {
    notificationContainer.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
  } else {
    notificationContainer.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
  }

  notificationContainer.textContent = message;

  document.body.appendChild(notificationContainer);

  // Remove the notification after 5 seconds
  setTimeout(() => {
    notificationContainer.remove();
  }, 5000);
}


function setupEnrollmentStatusListener(userCompany) {
  const companyRef = db.collection('companies').doc(userCompany);
  const usersRef = companyRef.collection('users');

  // Set up a real-time listener on the users collection
  usersRef.onSnapshot((snapshot) => {
    snapshot.docChanges().forEach((change) => {
      if (change.type === 'modified') {
        const userData = change.doc.data();
        const userId = change.doc.id;
        updateLearnerUI(userId, userData);
      }
    });
  }, (error) => {
    console.error("Error setting up enrollment status listener:", error);
  });
}

function updateLearnerUI(userId, userData) {
  const learnerCard = document.querySelector(`.learner-card[data-learner-id="${userId}"]`);
  if (learnerCard) {
    const statusElement = learnerCard.querySelector('.learner-status');
    const checkbox = learnerCard.querySelector('.enroll-checkbox');
    if (userData.enrollmentStatus === 'enrolled') {
      learnerCard.classList.remove('not-enrolled', 'processing', 'pending', 'bg-yellow-50', 'bg-orange-50');
      learnerCard.classList.add('enrolled', 'bg-green-50');
      statusElement.textContent = 'Enrolled';
      statusElement.classList.remove('text-gray-500', 'text-yellow-600', 'text-orange-600');
      statusElement.classList.add('text-green-600');
      checkbox.checked = true;
      checkbox.disabled = true;
    } else if (userData.enrollmentStatus === 'processing') {
      learnerCard.classList.remove('not-enrolled', 'enrolled', 'pending', 'bg-green-50', 'bg-orange-50');
      learnerCard.classList.add('processing', 'bg-yellow-50');
      statusElement.textContent = 'Processing';
      statusElement.classList.remove('text-gray-500', 'text-green-600', 'text-orange-600');
      statusElement.classList.add('text-yellow-600');
      checkbox.checked = true;
      checkbox.disabled = true;
    } else {
      learnerCard.classList.remove('enrolled', 'processing', 'bg-green-50', 'bg-yellow-50', 'pending', 'bg-orange-50');
      learnerCard.classList.add('not-enrolled');
      statusElement.textContent = 'Not Enrolled';
      statusElement.classList.remove('text-green-600', 'text-yellow-600', 'text-orange-600');
      statusElement.classList.add('text-gray-500');
      checkbox.checked = false;
      checkbox.disabled = false;
    }

    // Update the pathways data
    const learner = pathways.flatMap(p => p.learners).find(l => l.id === userId);
    if (learner) {
      learner.enrollmentStatus = userData.enrollmentStatus;
      learner.enrolled = userData.enrollmentStatus === 'enrolled';
    }

    // Update the selected learners set and total cost
    const learnerName = learnerCard.getAttribute('data-learner-name');
    if (userData.enrollmentStatus === 'enrolled' || userData.enrollmentStatus === 'processing') {
      selectedLearners.delete(learnerName);
    }
    updateTotalCost();
  }
}

function hasUnenrolledLearnersInOtherPathways(currentPathwayId) {
  return pathways.some(pathway =>
    pathway.id !== currentPathwayId &&
    pathway.learners.some(learner =>
      !learner.enrolled && learner.enrollmentStatus !== 'processing'
    )
  );
}


window.onload = initializeEnrollment;
window.onload = function () {
  initializeEnrollment();
};

function updateCourseSelectionButtonState() {
  const confirmButton = document.querySelector('.confirm-button');
  if (!confirmButton) return;

  const checkboxes = document.querySelectorAll('.course-checkbox');
  const hasSelectedCourses = Array.from(checkboxes).some(checkbox => checkbox.checked);

  confirmButton.disabled = !hasSelectedCourses;
  if (hasSelectedCourses) {
    confirmButton.classList.remove('disabled');
  } else {
    confirmButton.classList.add('disabled');
  }
}

window.handleBackToDetail = handleBackToDetail;
window.setupBackNavigation = setupBackNavigation;
window.initializeEnrollment = initializeEnrollment;

async function showEnrollmentDisclaimer() {
  const user = firebase.auth().currentUser;
  if (!user) return;

  const adminRef = db.collection('Admins').doc(user.email);
  const adminDoc = await adminRef.get();
  const adminData = adminDoc.data();

  if (adminData.isEnrollDisclaimerShown) return;

  // Create modal after a 1-second delay
  setTimeout(() => {
    const modalOverlay = document.createElement('div');
    modalOverlay.classList.add('disclaimer-modal-overlay');

    const modal = document.createElement('div');
    modal.classList.add('disclaimer-modal');
    modal.innerHTML = `
      <h3 class="text-xl font-semibold text-blue-600 mb-4">Important Notice</h3>
      <div class="space-y-4">
        <p class="text-gray-600">By proceeding with enrollment actions, you confirm that:</p>
        <ul class="list-disc list-inside text-gray-600 space-y-2 ml-4">
          <li class="flex items-start">
            <span class="text-blue-600 mr-2">•</span>
            <span>You have the authority to enroll learners in your organization</span>
          </li>
          <li class="flex items-start">
            <span class="text-blue-600 mr-2">•</span>
            <span>You understand that enrollments may incur costs as specified</span>
          </li>
          <li class="flex items-start">
            <span class="text-blue-600 mr-2">•</span>
            <span>You are authorised to make decisions regarding learning and development</span>
          </li>
        </ul>
        <p class="text-gray-600 mt-4">Please ensure you have the necessary permissions within your organization before proceeding.</p>
      </div>
      <div class="mt-6 flex justify-end space-x-4">
        <button id="disclaimerConfirm" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          I Understand
        </button>
      </div>
    `;

    modalOverlay.appendChild(modal);
    document.body.appendChild(modalOverlay);

    // Trigger animation
    setTimeout(() => {
      modalOverlay.classList.add('disclaimer-modal-show');
    }, 100);

    document.getElementById('disclaimerConfirm').addEventListener('click', async () => {
      try {
        // Show loading overlay before database operation
        showLoadingOverlay();

        await adminRef.update({
          isEnrollDisclaimerShown: true
        });

        modalOverlay.classList.remove('disclaimer-modal-show');
        setTimeout(() => {
          modalOverlay.remove();
          // Hide loading overlay after modal is removed
          hideLoadingOverlay();
        }, 300);
      } catch (error) {
        console.error('Error updating disclaimer status:', error);
        // Hide loading overlay in case of error
        hideLoadingOverlay();
        // Optionally show an error notification
        showNotification('Failed to update settings. Please try again.', 'error');
      }
    });
  }, 1000);
}