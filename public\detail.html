<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Learning Pathway</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
        }
        .glass-container {
            background: rgba(255, 255, 255, 0.87);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        .scrollable-section {
            max-height: 75vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #D1D5DB #F3F4F6;
        }
        .scrollable-section::-webkit-scrollbar {
            width: 6px;
        }
        .scrollable-section::-webkit-scrollbar-track {
            background: rgba(243, 244, 246, 0.5);
        }
        .scrollable-section::-webkit-scrollbar-thumb {
            background-color: rgba(209, 213, 219, 0.5);
            border-radius: 20px;
        }
        #employee-list.scrollable-section {
            max-height: 350px;
        }
        .card {
    background-color: rgba(255, 255, 255, 0.925);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

/* Updated button styles with new color palette */
.btn {
    display: inline-block;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary {
    background-color: #1547bb;
    color: white;
}

.btn-primary:hover {
    background-color: #1040a0;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(21, 71, 187, 0.25);
}

.btn-primary:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(21, 71, 187, 0.25);
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    z-index: -1;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.btn:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Ensure card hover doesn't affect button */
.card:hover .btn {
    transform: none;
    box-shadow: none;
}



        h2.title-major, h3.title-major {
            color: #121c41;
        }
        .course-category {
        border-bottom: 2px solid #e5e7eb;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
    }

    .course-category:last-child {
        border-bottom: none;
    }

    .category-title {
        color: #121c41;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-left: 0.5rem;
        border-left: 4px solid #1547bb;
    }
/* Styling for course category scrollbar */
#course-list::-webkit-scrollbar {
  width: 8px;
}

#course-list::-webkit-scrollbar-track {
  background: #e5e7eb;
  border-radius: 4px;
}

#course-list::-webkit-scrollbar-thumb {
  background: #1547bb;
  border-radius: 4px;
}

#course-list::-webkit-scrollbar-thumb:hover {
  background: #121c41;
}

/* Styling for employee list scrollbar */
#employee-list::-webkit-scrollbar {
  width: 8px;
}

#employee-list::-webkit-scrollbar-track {
  background: #e5e7eb;
  border-radius: 4px;
}

#employee-list::-webkit-scrollbar-thumb {
  background: #1547bb;
  border-radius: 4px;
}

#employee-list::-webkit-scrollbar-thumb:hover {
  background: #121c41;
}

/* Firefox styles */
#course-list, #employee-list {
  scrollbar-width: thin;
  scrollbar-color: #1547bb #e5e7eb;
}

.scrollable-section {
  max-height: 80vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollable-section::-webkit-scrollbar {
  width: 6px;
}

.scrollable-section::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-section::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
/* Fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
.card {
    @apply bg-white rounded-lg shadow-sm transition-all duration-200;
    transform-style: preserve-3d;
    position: relative;
}

/* Remove any transform on the card itself */
.card:hover {
    @apply shadow-md;
    transform: none;
}

/* Ensure content stays above card */
.card > * {
    position: relative;
    z-index: 2;
}

.card p {
    @apply mb-4;
}

/* Style for the "Learn more" link */
.card .recommendations-link {
    @apply cursor-pointer transition-colors duration-200;
    color: #1547bb;
    position: relative;
    z-index: 3;
}

.card .recommendations-link:hover {
    color: #121c41;
}

#book-now-btn {
    background-color: #1547bb;
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
    position: relative;
    z-index: 3;
}

#book-now-btn:hover {
    background-color: #121c41;
}

#book-now-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #1547bb;
}

.card.no-hover {
    @apply hover:shadow-sm;
    transform: none !important;
}

/* Scroll indicator styling */
#scroll-indicator {
    background-color: #1547bb;
}

#scroll-indicator:hover {
    background-color: #121c41;
}

/* Additional utility classes for the new color palette */
.text-primary {
    color: #1547bb;
}

.text-primary-dark {
    color: #121c41;
}

.bg-primary {
    background-color: #1547bb;
}

/* Filter button styles */
.filter-button {
    transition: all 0.2s ease-in-out;
}

.filter-button:hover {
    transform: translateY(-1px);
}

.filter-button.active {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bg-primary-dark {
    background-color: #121c41;
}

.border-primary {
    border-color: #1547bb;
}

/* Search and filter container styles */
.search-filter-container {
    gap: 0.5rem;
}

.search-container {
    margin-right: 0.5rem;
}

.filter-buttons-container {
    display: flex;
    align-items: center;
}

/*
 * Comprehensive Mobile-Responsive Redesign for Detail Page
 * This completely revamps the mobile experience while preserving desktop layout
 */

/* Reset container padding for mobile */
@media (max-width: 768px) {
  body {
    padding: 0.5rem !important;
  }

  main.container {
    padding: 0.75rem !important;
    max-width: 100% !important;
    margin: 0 auto !important;
  }

  .glass-container {
    border-radius: 15px;
    padding: 1rem !important;
  }

  /* Ensure grid properly stacks on mobile */
  .grid.grid-cols-1.lg\:grid-cols-3.gap-8 {
    gap: 1rem !important;
  }
}

/* Fix search and filter section */
@media (max-width: 768px) {
  /* Make header section stack and use full width */
  .lg\:col-span-2 > div:first-child {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    width: 100% !important;
  }

  /* Ensure the Courses title appears above search and filter */
  .lg\:col-span-2 > div:first-child > h2 {
    margin-bottom: 0.5rem !important;
    width: 100% !important;
  }

  /* Make search-filter-container take full width */
  .lg\:col-span-2 > div:first-child > div.search-filter-container {
    width: 100% !important;
  }

  /* Make search and filter container take full width and stack */
  .search-filter-container {
    flex-direction: column !important;
    width: 100% !important;
    gap: 0.75rem !important;
    align-items: flex-start !important;
  }

  /* Full width search */
  .search-container {
    width: 100% !important;
    margin-right: 0 !important;
  }

  #course-search {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Filter buttons container */
  .filter-buttons-container {
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: none !important; /* Firefox */
    padding-bottom: 6px !important;
  }

  /* Hide scrollbar but allow scrolling */
  .filter-buttons-container::-webkit-scrollbar {
    display: none !important;
  }

  /* Filter buttons proper sizing and spacing */
  .filter-button {
    flex: 0 0 auto !important;
    font-size: 0.7rem !important;
    padding: 0.5rem 0.75rem !important;
    margin: 0 0.25rem !important;
    white-space: nowrap !important;
    min-width: auto !important;
  }

  /* First and last filter button proper margins */
  .filter-button:first-child {
    margin-left: 0 !important;
  }

  .filter-button:last-child {
    margin-right: 0 !important;
  }
}

/* Course list improvements */
@media (max-width: 768px) {
  #course-list {
    padding-right: 0 !important;
  }

  /* Card improvements for mobile */
  .card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  .card h4 {
    font-size: 0.95rem !important;
    margin-bottom: 0.25rem !important;
  }

  .card p {
    font-size: 0.8rem !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
  }

  /* Skill type badge */
  .card span.px-2.py-1.rounded-full.text-xs {
    padding: 0.15rem 0.5rem !important;
    font-size: 0.65rem !important;
  }
}

/* Enrollment card improvements */
@media (max-width: 768px) {
  .card.no-hover.p-6 {
    padding: 0.75rem !important;
  }

  /* Typography adjustments */
  .title-major.text-xl {
    font-size: 1.1rem !important;
  }

  .title-major.text-2xl {
    font-size: 1.25rem !important;
  }

  /* Button size adjustments */
  .btn.btn-primary {
    padding: 0.5rem 1rem !important;
    font-size: 0.85rem !important;
  }
}

/* Employee list improvements */
@media (max-width: 768px) {
  #employee-list {
    max-height: 200px !important;
  }
}

/* More specific fixes for extremely small screens */
@media (max-width: 370px) {
  .filter-button {
    padding: 0.4rem 0.5rem !important;
    font-size: 0.65rem !important;
  }

  /* Stack skill badge below title in course cards */
  .flex.justify-between.items-start {
    flex-direction: column !important;
  }

  .card span.bg-blue-100,
  .card span.bg-purple-100 {
    margin-top: 0.25rem !important;
    align-self: flex-start !important;
  }
}

/* Styles for dynamically added search input in overlay */
#searchInput {
    width: 100%;
    max-width: 100%;
}

/* Responsive container padding */
@media (max-width: 640px) {
    .glass-container {
        padding: 1rem;
    }

    main.container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
}
    </style>
</head>
<body class="p-4 sm:p-6 lg:p-8">
    <main class="container mx-auto max-w-7xl glass-container p-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <section class="mb-8">
                    <h2 id="learning-path" class="title-major text-2xl font-semibold mb-4 text-gray-800">Intermediate Learning Pathway</h2>
                    <p id="description" class="text-sm text-gray-600 mb-6"></p>
                    <div class="card no-hover p-6">
                        <h3 class="title-major text-lg font-semibold mb-2 text-gray-800">Ready to Upskill?</h3>
                        <p class="text-sm text-gray-600 mb-4">Fetching enrollment status....</p>
                        <button id="book-now-btn" class="btn btn-primary">Enroll Now</button>
                    </div>
                </section>

                <div class="section">
                    <h2 id="employees-on-this-pathway" class="title-major text-xl font-semibold mb-4 text-gray-800">Employees on this pathway</h2>
                    <div class="relative">
                      <div id="employee-list" class="scrollable-section pr-2 max-h-80 overflow-y-auto">
                        <!-- Employee list items will be dynamically added here -->
                      </div>

                      <div id="scroll-indicator" class="hidden absolute bottom-0 left-1/2 transform -translate-x-1/2 text-white rounded-full shadow-lg cursor-pointer transition-all duration-300 flex items-center justify-center" style="width: 25px; height: 25px;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>

                      </div>

                    </div>
                  </div>

            </div>
            <div class="lg:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="title-major text-xl font-semibold text-gray-800">Courses</h2>
                    <div class="flex items-center search-filter-container">
                        <div class="relative search-container">
                            <input type="text" id="course-search" placeholder="Search courses..." class="pl-8 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <svg class="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="flex filter-buttons-container">
                            <button id="filter-all" class="filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-primary text-white mr-1">All</button>
                            <button id="filter-digital" class="filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-blue-100 text-blue-600 hover:bg-blue-200 mx-1">Digital Skills</button>
                            <button id="filter-soft" class="filter-button px-3 py-1.5 text-xs font-medium rounded-md bg-purple-100 text-purple-600 hover:bg-purple-200 ml-1">Soft Skills</button>
                        </div>
                    </div>
                </div>
                <div id="course-list" class="scrollable-section pr-2 mb-6"></div>
                <div class="card no-hover p-6">
                    <h3 class="title-major text-lg font-semibold mb-2 text-gray-800">Ready to Upskill?</h3>
                    <p class="text-sm text-gray-600 mb-4">Fetching enrollment status....</p>
                    <button id="duplicate-book-now-btn" class="btn btn-primary">Enroll Now</button>
                </div>
            </div>
        </div>
    </main>
    <script src="detail.js"></script>
</body>
</html>
