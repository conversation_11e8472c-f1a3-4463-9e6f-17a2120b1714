<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="dashboard-animations.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/litepicker/dist/css/litepicker.css">
    <style>
        .search-button {
            z-index: 10;
        }

        #email-modal {
            z-index: 9999;
        }
        #email-loading-overlay {
            z-index: 9999;
        }
        .skeleton-card {
            display: none;
        }

        /* Hide card content by default until data is loaded */
        .card-content {
            display: none;
        }

        .card-empty {
            display: none;
        }

        .skeleton-text {
            background-color: #e0e0e0;
            height: 20px;
            margin-bottom: 10px;
            border-radius: 4px;
            animation: pulse 1.5s infinite ease-in-out;
        }

        @keyframes pulse {
            0% {
                background-color: #e0e0e0;
            }
            50% {
                background-color: #f0f0f0;
            }
            100% {
                background-color: #e0e0e0;
            }
        }

        /* Ensure tooltip visibility */
        .tooltip {
            visibility: hidden;
            opacity: 0;
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            z-index: 10;
            transition: opacity 0.3s, visibility 0.3s;
            max-width: 300px;
            white-space: normal;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        td:hover .tooltip {
            visibility: visible;
            opacity: 1;
        }

        /* Filter dropdown positioning - increase z-index significantly */
        #filter-options {
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 2px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000; /* Increased from 50 to 1000 */
            transition: opacity 0.2s ease-in-out;
            background-color: white; /* Ensure background is opaque */
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        #filter-options.dropdown-up {
            top: auto;
            bottom: 100%;
            margin-top: 0;
            margin-bottom: 2px;
        }

        /* Ensure dropdown is above other content */
        .relative {
            position: relative;
            z-index: 100; /* Add z-index to create stacking context */
        }

        /* Give table a lower z-index */
        .bg-white.rounded-lg.shadow-lg.overflow-hidden.animate-fade-in {
            position: relative;
            z-index: 10; /* Lower z-index for the table container */
        }

        /* Table container */
        .overflow-x-auto {
            position: relative;
            z-index: 10; /* Same as parent */
        }

        /* Filter dropdown styling */
        #filter-options {
            background-color: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 240px;
            z-index: 1050;
            max-height: 400px;
            overflow-y: auto;
        }

        /* Make sure the filter button is above the table */
        #filter-button {
            position: relative;
            z-index: 1040;
        }

        /* Ensure the table doesn't create a new stacking context with a higher z-index */
        .bg-white.rounded-lg.shadow-lg.overflow-hidden.animate-fade-in {
            z-index: 5;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-950">
<div class="container mx-auto px-4 md:px-6 mt-4">
<!-- assessments cards -->
<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="card dashboard-card" id="total-employees">
        <div class="skeleton-card">
            <div class="skeleton-text w-1/2"></div>
            <div class="skeleton-text w-1/4"></div>
            <div class="skeleton-text w-3/4"></div>
        </div>
        <div class="card-content">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold">Total Employees</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <div class="mt-4">
                <div class="text-4xl font-bold">0</div>
            </div>
        </div>
        <div class="card-empty flex flex-col items-center justify-center space-y-4">
            <div class="flex items-center justify-between w-full">
                <h2 class="text-lg font-semibold">Total Employees</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <p class="text-center text-gray-500 font-light">No employees yet</p>
        </div>
    </div>
    <div class="card dashboard-card" id="completed">
        <div class="skeleton-card">
            <div class="skeleton-text w-1/2"></div>
            <div class="skeleton-text w-1/4"></div>
            <div class="skeleton-text w-3/4"></div>
        </div>
        <div class="card-content">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold">Completed</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="mt-4">
                <div class="text-4xl font-bold">0</div>
            </div>
        </div>
        <div class="card-empty flex flex-col items-center justify-center space-y-4">
            <div class="flex items-center justify-between w-full">
                <h2 class="text-lg font-semibold">Completed</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <p class="text-center text-gray-500 font-light">No completed assessments</p>
        </div>
    </div>
    <div class="card dashboard-card" id="aborted">
        <div class="skeleton-card">
            <div class="skeleton-text w-1/2"></div>
            <div class="skeleton-text w-1/4"></div>
            <div class="skeleton-text w-3/4"></div>
        </div>
        <div class="card-content">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold">Aborted</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="mt-4">
                <div class="text-4xl font-bold">0</div>
            </div>
        </div>
        <div class="card-empty flex flex-col items-center justify-center space-y-4">
            <div class="flex items-center justify-between w-full">
                <h2 class="text-lg font-semibold">Aborted</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <p class="text-center text-gray-500 font-light">No aborted assessments</p>
        </div>
    </div>
    <div class="card dashboard-card" id="pending">
        <div class="skeleton-card">
            <div class="skeleton-text w-1/2"></div>
            <div class="skeleton-text w-1/4"></div>
            <div class="skeleton-text w-3/4"></div>
        </div>
        <div class="card-content">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold">Pending</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="mt-4">
                <div class="text-4xl font-bold">0</div>
            </div>
        </div>
        <div class="card-empty flex flex-col items-center justify-center space-y-4">
            <div class="flex items-center justify-between w-full">
                <h2 class="text-lg font-semibold">Pending</h2>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <p class="text-center text-gray-500 font-light">No pending assessments</p>
        </div>
    </div>
</div>
<!-- Filters and Controls -->
<div class="px-4 py-4 bg-white rounded-lg shadow-lg mb-4 animate-fade-in">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <h2 class="text-2xl font-semibold text-gray-800">All Assessments</h2>
        <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
            <div class="relative w-full sm:w-auto">
                <input type="text" id="search-input" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Search by employee name...">
            </div>
            <div class="relative w-full sm:w-auto">
                <input type="text" id="date-range-picker" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Select Date Range">
            </div>
            <div class="flex space-x-2 w-full sm:w-auto">
                <button id="filter-button" class="w-full sm:w-auto px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-2 hidden sm:inline">Filter</span>
                </button>
                <button id="clear-filters" class="hidden w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200" title="Clear all filters">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="relative w-full sm:w-auto">
                <select id="rows-per-page" class="block appearance-none w-full bg-white border border-gray-300 hover:border-gray-500 px-4 py-2 pr-8 rounded-lg leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="10">10 rows</option>
                    <option value="25">25 rows</option>
                    <option value="50">50 rows</option>
                    <option value="100">100 rows</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Filter Modal -->
<div id="filter-modal" class="hidden fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 animate-fade-in" style="max-height: 90vh; overflow-y: auto;">
        <div class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
            <h3 class="text-base font-medium text-gray-800">Filter Assessments</h3>
            <button id="close-filter-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="px-4 py-3">
            <div class="space-y-4">
                <!-- Status Filters -->
                <div>
                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Status</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="status" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="completed">
                            <span class="text-gray-700">Completed</span>
                        </label>
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="status" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="started">
                            <span class="text-gray-700">Started</span>
                        </label>
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="status" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="aborted">
                            <span class="text-gray-700">Aborted</span>
                        </label>
                    </div>
                </div>

                <!-- Divider -->
                <div class="border-t border-gray-100"></div>

                <!-- Assessment Type Filters -->
                <div>
                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Assessment Type</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="assessmentType" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="digital">
                            <span class="text-gray-700">Digital Skills</span>
                        </label>
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="assessmentType" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="soft">
                            <span class="text-gray-700">Soft Skills</span>
                        </label>
                    </div>
                </div>

                <!-- Divider -->
                <div class="border-t border-gray-100"></div>

                <!-- Pathway Filters -->
                <div>
                    <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Pathway</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="pathway" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="essentials">
                            <span class="text-gray-700">Essentials</span>
                        </label>
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="pathway" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="intermediate">
                            <span class="text-gray-700">Intermediate</span>
                        </label>
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="pathway" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="advanced">
                            <span class="text-gray-700">Advanced</span>
                        </label>
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" name="pathway" class="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-300 focus:ring-blue-500" value="champions">
                            <span class="text-gray-700">Champions</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-100 flex justify-end space-x-2">
            <button id="reset-filters" class="px-3 py-1.5 bg-white border border-gray-300 rounded-md text-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                Reset
            </button>
            <button id="apply-filters" class="px-3 py-1.5 bg-blue-600 border border-transparent rounded-md text-xs font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                Apply Filters
            </button>
        </div>
    </div>
</div>

<!-- Assessments Table -->
<div class="bg-white rounded-lg shadow-lg overflow-hidden animate-fade-in" style="animation-delay: 0.2s">
    <div class="overflow-x-auto">
        <div class="max-h-96 overflow-y-auto">
        <table class="w-full table-auto">
            <thead class="bg-gray-50 table-header">
                <tr>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">Employee</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">Assessment Type</th>
                    <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">Analysis</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">Learning Paths</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Date</th>
                    <th id="actions-header" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <!-- Skeleton Loaders -->
                <tr class="skeleton-row">
                    <td class="skeleton-text w-full h-6 mb-2"></td>
                    <td class="skeleton-text w-3/4 h-4 mb-2"></td>
                    <td class="skeleton-text w-1/2 h-4"></td>
                    <td class="skeleton-text w-3/4 h-4"></td>
                    <td class="skeleton-text w-1/2 h-4"></td>
                    <td class="skeleton-text w-1/4 h-4"></td>
                </tr>
                <tr class="skeleton-row">
                    <td class="skeleton-text w-full h-6 mb-2"></td>
                    <td class="skeleton-text w-3/4 h-4 mb-2"></td>
                    <td class="skeleton-text w-1/2 h-4"></td>
                    <td class="skeleton-text w-3/4 h-4"></td>
                    <td class="skeleton-text w-1/2 h-4"></td>
                    <td class="skeleton-text w-1/4 h-4"></td>
                </tr>
                <tr class="skeleton-row">
                    <td class="skeleton-text w-full h-6 mb-2"></td>
                    <td class="skeleton-text w-3/4 h-4 mb-2"></td>
                    <td class="skeleton-text w-1/2 h-4"></td>
                    <td class="skeleton-text w-3/4 h-4"></td>
                    <td class="skeleton-text w-1/2 h-4"></td>
                    <td class="skeleton-text w-1/4 h-4"></td>
                </tr>
                <!-- Rows will be populated by JavaScript -->
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="5" class="px-4 py-2">
                        <div class="flex justify-between items-center animate-fade-in" style="animation-delay: 0.3s">
                            <div>
                                Showing <span id="start-row">1</span> to <span id="end-row">10</span> of <span id="total-rows">0</span> entries
                            </div>
                            <div>
                                <button id="prev-page" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2">&lt; Previous</button>
                                <button id="next-page" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2">Next &gt;</button>
                            </div>
                        </div>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
    </div>
</div>

</div>
<div id="email-modal" class="hidden fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center">
    <div class="relative bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div class="absolute top-4 right-4">
            <button id="close-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Send Follow Up Email</h3>
            <div class="space-y-4">
                <input type="email" id="email-to" placeholder="To" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" readonly>
                <input type="text" id="email-subject" placeholder="Subject" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <textarea id="email-body" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Email body..."></textarea>
            </div>
            <div class="mt-6">
                <button id="send-email" class="w-full px-4 py-2 bg-blue-600 text-white font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                    Send Email
                </button>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/litepicker/dist/bundle.js"></script>
<!-- Include User Journey Tracker -->
<script src="user-journey-tracker.js"></script>
<script src="assessments.js"></script>
</body>
</html>