(function(global) {
    let isModalInitialized = false;
    let isClosing = false;
    let currentChartInstance = null;
    let currentData = null;
    let currentAnalysisType = null;

    async function showSkillsGapAnalysis(data = null) {
        try {
            // Show loading overlay immediately
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            if (data) {
                currentData = data;  // Store the data

                // Determine which analysis type to show first
                if (currentData.metadata && currentData.metadata.availableAnalysisTypes) {
                    currentAnalysisType = currentData.metadata.availableAnalysisTypes[0];
                } else if (currentData.digitalSkills) {
                    currentAnalysisType = 'digitalSkills';
                } else if (currentData.softSkills) {
                    currentAnalysisType = 'softSkills';
                } else {
                    // Backward compatibility for old data structure
                    currentAnalysisType = 'legacy';
                }
            }

            if (isModalInitialized) {
                await resetAndShowModal();
                return;
            }

            isClosing = false;

            // Create modal structure
            const overlay = document.createElement('div');
            overlay.id = 'skills-gap-overlay';
            overlay.className = 'skills-modal-overlay';
            // Set initial opacity to 0 for fade-in effect
            overlay.style.opacity = '0';

            if (currentData) {
                overlay.innerHTML = createModalHTML(currentData);
            } else {
                throw new Error('Invalid data structure for modal creation');
            }

            document.body.appendChild(overlay);
            isModalInitialized = true;

            // Initialize event listeners
            initializeEventListeners(overlay);

            // Create radar chart with actual data
            await createRadarChart(getCurrentAnalysisData());

            // Animate modal appearance with a smooth fade
            requestAnimationFrame(() => {
                if (!isClosing) {
                    // Use a small timeout to ensure smooth transition
                    setTimeout(() => {
                        overlay.style.opacity = '1';
                        const modalContent = overlay.querySelector('.skills-modal-content');
                        if (modalContent) {
                            modalContent.style.opacity = '1';
                            modalContent.style.transform = 'scale(1)';
                        }

                        // Hide loading overlay after modal is visible
                        if (typeof hideLoadingOverlay === 'function') {
                            hideLoadingOverlay();
                        }
                    }, 50);
                }
            });

            addStyles();
        } catch (error) {
            console.error('Error showing skills gap modal:', error);
            // Hide loading overlay if there's an error
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            throw error;
        }
    }

    function getCurrentAnalysisData() {
        if (currentAnalysisType === 'legacy') {
            return currentData; // Original structure for backward compatibility
        } else {
            return currentData[currentAnalysisType];
        }
    }

    async function resetAndShowModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new currentData
        overlay.innerHTML = createModalHTML(currentData);

        // Re-initialize event listeners
        initializeEventListeners(overlay);

        // Recreate the chart
        await createRadarChart(getCurrentAnalysisData());

        // Show modal with improved animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0'; // Start with opacity 0

        requestAnimationFrame(() => {
            // Use a small timeout for smoother transition
            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.skills-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }

                // Hide loading overlay after modal is visible
                if (typeof hideLoadingOverlay === 'function') {
                    hideLoadingOverlay();
                }
            }, 50);
        });
    }

    function createCompetencyCard(competency, data) {
        // Check if we have any strength areas
        const hasStrengths = Array.isArray(data.strengthAreas) && data.strengthAreas.length > 0;

        // Check if we have any gap areas
        const hasGaps = Array.isArray(data.gapAreas) && data.gapAreas.length > 0;

        // Create the strength areas HTML if we have any
        const strengthAreasHTML = hasStrengths
            ? `
                <div class="skills-strength-areas">
                    <h4>Strengths</h4>
                    ${data.strengthAreas.map(area => `
                        <span class="skills-badge skills-strength">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the gap areas HTML if we have any
        const gapAreasHTML = hasGaps
            ? `
                <div class="skills-gap-areas">
                    <h4>Gaps</h4>
                    ${data.gapAreas.map(area => `
                        <span class="skills-badge skills-gap">${area}</span>
                    `).join('')}
                </div>
            `
            : '';

        // Create the no data message if we don't have any strengths or gaps
        const noDataHTML = !hasStrengths && !hasGaps
            ? `<div class="skills-no-data">No data available</div>`
            : '';

        return `
            <div class="skills-competency-card">
                <h3>${competency}</h3>
                <div class="skills-proficiency-meter">
                    <div class="skills-progress-bar">
                        <div class="skills-progress" style="width: ${data.proficiencyLevel}"></div>
                    </div>
                    <span class="skills-proficiency-level">${data.proficiencyLevel}</span>
                </div>
                <div class="skills-areas-section">
                    ${strengthAreasHTML}
                    ${gapAreasHTML}
                    ${noDataHTML}
                </div>
            </div>
        `;
    }

    async function createRadarChart(data = null) {
        try {
            if (!data || !data.report || !data.report.competencyAnalysis) {
                throw new Error('Invalid data for radar chart creation');
            }

            // Cleanup existing chart if any
            if (currentChartInstance) {
                currentChartInstance.destroy();
                currentChartInstance = null;
            }

            const chartData = Object.entries(data.report.competencyAnalysis).map(([name, compData]) => ({
                axis: name,
                value: parseInt(compData.proficiencyLevel) || 0
            }));

            // Wait for Chart.js if not loaded
            if (!window.Chart) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js';
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }

            const container = document.querySelector('#radar-chart');
            if (!container) {
                throw new Error('Radar chart container not found');
            }

            // Clear container and create new canvas
            container.innerHTML = '';
            const ctx = document.createElement('canvas');
            container.appendChild(ctx);

            // Use different colors based on analysis type
            let borderColor, backgroundColor;

            if (currentAnalysisType === 'softSkills') {
                borderColor = '#7c3aed'; // Purple for soft skills
                backgroundColor = 'rgba(124, 58, 237, 0.1)';
            } else {
                borderColor = '#1e3a8a'; // Original blue color
                backgroundColor = 'rgba(30, 58, 138, 0.1)';
            }

            // Create chart for UI display (original size)
            currentChartInstance = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: chartData.map(d => d.axis),
                    datasets: [{
                        data: chartData.map(d => d.value),
                        backgroundColor: backgroundColor,
                        borderColor: borderColor,
                        pointBackgroundColor: borderColor
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { display: false },
                            grid: { color: '#d1d5db' },
                            angleLines: { color: '#d1d5db' }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        } catch (error) {
            console.error('Error creating radar chart:', error);
            throw error;
        }
    }

    function createModalHTML(data) {
        // For backward compatibility
        let activeData;
        let hasMultipleAnalysisTypes = false;
        let analysisTypes = [];

        if (data.metadata && data.metadata.availableAnalysisTypes) {
            analysisTypes = data.metadata.availableAnalysisTypes;
            hasMultipleAnalysisTypes = analysisTypes.length > 1;
            activeData = getCurrentAnalysisData();
        } else if (data.digitalSkills) {
            activeData = data.digitalSkills;
            hasMultipleAnalysisTypes = !!data.softSkills;
            if (hasMultipleAnalysisTypes) {
                analysisTypes = ['digitalSkills'];
                if (data.softSkills) analysisTypes.push('softSkills');
            }
        } else if (data.softSkills) {
            activeData = data.softSkills;
            analysisTypes = ['softSkills'];
        } else {
            activeData = data; // Original structure for backward compatibility
        }

        // Create analysis switcher UI if multiple types are available
        const analysisSwitcher = hasMultipleAnalysisTypes
            ? `
            <div class="skills-analysis-switcher">
                ${analysisTypes.map(type => `
                    <button
                        class="skills-analysis-btn${type === currentAnalysisType ? ' active' : ''}"
                        data-analysis-type="${type}">
                        ${getAnalysisTypeLabel(type)}
                    </button>
                `).join('')}
            </div>
            `
            : '';

        const competencyCards = activeData
            ? Object.entries(activeData.report.competencyAnalysis)
                  .map(([competency, competencyData]) =>
                      createCompetencyCard(competency, competencyData)
                  )
                  .join('')
            : '';

        // More flexible conditions for showing the enrollment button
        const shouldShowEnrollButton = activeData
            && activeData.report
            && (activeData.report.learningPath || activeData.report.currentPath || currentData.metadata?.currentPath);

        const enrollmentButton = shouldShowEnrollButton
            ? `
            <div class="skills-cta-section">
                <button id="enroll-now-button" class="skills-enroll-now-button">Start Assigning This Training</button>
            </div>
            `
            : '';

        // Core recommendations
        const coreRecommendations = activeData.recommendations.map(rec => `
            <li>
                <strong>${rec.course}</strong>
                <p>${rec.reason}</p>
            </li>
        `).join('');

        // Additional (collapsible) recommendations
        const crossPathSection = activeData.other_learning_paths_courses && activeData.other_learning_paths_courses.length > 0
            ? `
                <div class="skills-recommendations-section skills-cross-path-recommendations">
                    <div class="skills-cross-path-header">
                        <h3>Additional Learning Path Recommendations</h3>
                        <button id="toggle-cross-paths-btn" class="toggle-cross-paths-btn">Show/Hide</button>
                    </div>
                    <div id="skills-cross-paths-collapsible" class="skills-cross-paths-collapsible">
                        <ul>
                            ${
                                activeData.other_learning_paths_courses.map(rec => `
                                    <li>
                                        <strong>${rec.course}</strong>
                                        <p>${rec.reason}</p>
                                    </li>
                                `).join('')
                            }
                        </ul>
                    </div>
                </div>
            `
            : '';

        return `
            <div class="skills-modal-content">
                <div class="skills-modal-header">
                    <div class="skills-modal-title-container">
                        <h2 class="skills-modal-employee-title">
                            ${activeData && activeData.report ? `${activeData.report.employeeName || ''} ${activeData.report.role ? `- ${activeData.report.role}` : ''}` : ''}
                        </h2>
                        <h3 class="skills-modal-subtitle">Skills Gap Analysis Report</h3>
                    </div>
                    <div class="skills-modal-actions">
                        <button id="export-pdf-button" class="skills-export-pdf-button">Export to PDF</button>
                        <button id="close-skills-modal" class="skills-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="skills-modal-body">
                    ${analysisSwitcher}

                    <div id="radar-chart" class="skills-chart-container"></div>

                    <div class="skills-competency-grid">
                        ${competencyCards}
                    </div>

                    <div class="skills-learning-path-section">
                        <h3>Learning Path Recommendations</h3>
                        <p>${activeData ? activeData.report.summary : ''}</p>
                    </div>

                    <div class="skills-recommendations-container">
                        <div class="skills-recommendations-section skills-primary-recommendations">
                            <h3>Core Learning Path Recommendations</h3>
                            <ul>
                                ${coreRecommendations}
                            </ul>
                        </div>
                        ${crossPathSection}
                    </div>

                    ${enrollmentButton}
                </div>
            </div>
        `;
    }

    function getAnalysisTypeLabel(type) {
        switch (type) {
            case 'digitalSkills':
                return 'Digital Skills';
            case 'softSkills':
                return 'Soft Skills';
            default:
                return type.charAt(0).toUpperCase() + type.slice(1).replace(/([A-Z])/g, ' $1');
        }
    }

    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .skills-modal-overlay {
                position: fixed;
                top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0, 0, 0, 0.4);
                display: flex;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.4s ease;
                z-index: 1000;
            }

            .skills-modal-content {
                background: #ffffff;
                border-radius: 6px;
                width: 80%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                opacity: 0;
                transform: scale(0.95);
                transition: all 0.4s ease;
                font-family: sans-serif;
                color: #374151;
                position: relative;
                font-size: 0.875rem;
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            }

            .skills-modal-header {
                padding: 0.75rem;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            /* Analysis type switcher */
            .skills-analysis-switcher {
                display: flex;
                justify-content: center;
                margin: 0 0 1.5rem 0;
                gap: 0.5rem;
            }

            .skills-analysis-btn {
                background: #f3f4f6;
                border: 1px solid #e5e7eb;
                border-radius: 20px;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
                cursor: pointer;
                transition: all 0.2s ease;
                color: #4b5563;
            }

            .skills-analysis-btn:hover {
                background: #e5e7eb;
            }

            .skills-analysis-btn.active {
                background: #1e3a8a;
                color: white;
                border-color: #1e3a8a;
            }

            .skills-analysis-btn.active[data-analysis-type="softSkills"] {
                background: #7c3aed;
                border-color: #7c3aed;
            }

            .skills-modal-title-container {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .skills-modal-employee-title {
                font-size: 1rem;
                font-weight: 600;
                color: #1e3a8a;
                margin: 0;
            }

            .skills-modal-subtitle {
                font-size: 0.8rem;
                font-weight: 500;
                color: #4b5563;
                margin: 0;
            }

            .skills-modal-actions {
                display: flex;
                gap: 0.5rem;
            }

            .skills-close-modal-button,
            .skills-export-pdf-button {
                background: none;
                border: 1px solid #1e3a8a;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                cursor: pointer;
                color: #1e3a8a;
            }

            .skills-close-modal-button svg {
                stroke: #1e3a8a;
            }

            .skills-export-pdf-button:hover {
                background: #1e3a8a;
                color: #fff;
            }

            .skills-modal-body {
                padding: 1rem;
                line-height: 1.4;
            }

            .skills-chart-container {
                position: relative;
                height: 300px;
                width: 80%;
                margin: 0 auto 1.5rem;
            }

            .skills-competency-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.75rem;
                margin-bottom: 1.5rem;
                padding: 0 1rem;
            }

            .skills-competency-card {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #ffffff;
            }

            .skills-competency-card h3 {
                margin: 0 0 0.5rem;
                font-size: 0.9rem;
                color: #1e3a8a;
            }

            /* Different color for soft skills competency cards */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-competency-card h3 {
                color: #7c3aed;
            }

            .skills-proficiency-meter {
                margin: 0.75rem 0;
                display: flex;
                align-items: center;
            }

            .skills-progress-bar {
                background: #e5e7eb;
                border-radius: 9999px;
                height: 6px;
                width: 100%;
                margin-right: 0.5rem;
                overflow: hidden;
            }

            .skills-progress {
                background: #f59e0b;
                border-radius: 9999px;
                height: 100%;
                transition: width 0.3s ease;
            }

            /* Different color for soft skills progress */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-progress {
                background: #8b5cf6;
            }

            .skills-proficiency-level {
                font-size: 0.8rem;
                color: #6b7280;
            }

            .skills-areas-section h4 {
                margin: 0.5rem 0 0.25rem;
                font-size: 0.8rem;
                font-weight: 600;
                color: #374151;
            }

            .skills-badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                border-radius: 9999px;
                font-size: 0.75rem;
                margin: 0.25rem 0.25rem 0 0;
            }

            .skills-badge.skills-strength {
                background: #f0f9ff;
                color: #1e3a8a;
            }

            /* Different colors for soft skills badges */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-badge.skills-strength {
                background: #f5f3ff;
                color: #7c3aed;
            }

            .skills-badge.skills-gap {
                background: #fef3f2;
                color: #9b1c1c;
            }

            .skills-learning-path-section {
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                padding: 1rem;
                background: #f9fafb;
                margin: 0 1rem 1.5rem;
            }

            .skills-learning-path-section h3 {
                margin-top: 0;
                font-size: 0.9rem;
                color: #1e3a8a;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            /* Different color for soft skills headers */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-learning-path-section h3 {
                color: #7c3aed;
            }

            .skills-recommendations-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                padding: 0 1rem;
            }

            .skills-recommendations-section {
                background: #f9fafb;
                border-radius: 6px;
                padding: 1rem;
                border-left: 4px solid transparent;
            }

            .skills-recommendations-section ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .skills-recommendations-section li {
                border-bottom: 1px solid #e5e7eb;
                padding: 0.5rem 0;
            }

            .skills-recommendations-section li:last-child {
                border-bottom: none;
            }

            .skills-primary-recommendations {
                border-left-color: #1e3a8a;
            }

            /* Different color for soft skills primary recommendations */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-primary-recommendations {
                border-left-color: #7c3aed;
            }

            .skills-cross-path-recommendations {
                border-left-color: #059669;
            }

            .skills-recommendations-section h3 {
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-weight: 600;
                font-size: 0.9rem;
                color: #374151;
            }

            .skills-cross-path-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.25rem;
            }

            .toggle-cross-paths-btn {
                background: none;
                border: 1px solid #059669;
                border-radius: 4px;
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
                cursor: pointer;
                color: #059669;
            }

            .toggle-cross-paths-btn:hover {
                background: #059669;
                color: #fff;
            }

            .skills-cta-section {
                text-align: center;
                margin-top: 2rem;
                margin-bottom: 1rem;
            }

            .skills-enroll-now-button {
                background: #1e3a8a;
                color: white;
                border: none;
                border-radius: 24px;
                padding: 0.75rem 2rem;
                cursor: pointer;
                font-weight: 600;
                font-size: 1rem;
                transition: background-color 0.2s ease, transform 0.2s ease;
                box-shadow: 0 4px 14px rgba(30,58,138,0.3);
            }

            /* Different color for soft skills enroll button */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-enroll-now-button {
                background: #7c3aed;
                box-shadow: 0 4px 14px rgba(124,58,237,0.3);
            }

            .skills-enroll-now-button:hover {
                background: #1e40af;
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(30,58,138,0.4);
            }

            /* Different hover color for soft skills enroll button */
            .skills-modal-body[data-analysis-type="softSkills"] .skills-enroll-now-button:hover {
                background: #6d28d9;
                box-shadow: 0 6px 20px rgba(124,58,237,0.4);
            }

            .skills-cross-paths-collapsible {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
                opacity: 0;
            }

            .skills-cross-paths-collapsible.expanded {
                max-height: 1000px;
                opacity: 1;
                transition: max-height 0.5s ease-in, opacity 0.3s ease-in;
            }

            /* Analysis fade transition */
            .skills-analysis-container {
                transition: opacity 0.3s ease-in-out;
            }

            .skills-analysis-container.fading-out {
                opacity: 0;
            }

            .skills-analysis-container.fading-in {
                opacity: 1;
            }

            /* No data available message */
            .skills-no-data {
                color: #6b7280;
                font-size: 0.85rem;
                text-align: center;
                padding: 0.75rem 0;
                font-style: italic;
            }
        `;
        document.head.appendChild(styleSheet);
    }

    function initializeEventListeners(overlay) {
        const closeButton = overlay.querySelector('#close-skills-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        overlay.addEventListener('click', overlayClickHandler);

        const exportPdfButton = overlay.querySelector('#export-pdf-button');
        if (exportPdfButton) {
            exportPdfButton.addEventListener('click', exportToPDF);
        }

        // Add event listener for enroll button with more robust handling
        const enrollButton = overlay.querySelector('#enroll-now-button');
        if (enrollButton) {
            // Don't pass a specific learning path - the handler will determine it from the active analysis type
            enrollButton.addEventListener('click', () => handleEnrollment());
        } else {
            console.log('Enrollment button not found in modal');
        }

        // Toggle cross-path recommendations with animation
        const toggleButton = overlay.querySelector('#toggle-cross-paths-btn');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                const collapsible = overlay.querySelector('#skills-cross-paths-collapsible');
                if (collapsible) {
                    collapsible.classList.toggle('expanded');
                    // Update initial display style if not set
                    if (collapsible.style.display === 'none') {
                        collapsible.style.display = 'block';
                    }
                }
            });
        }

        // Initialize analysis type switcher buttons
        const analysisBtns = overlay.querySelectorAll('.skills-analysis-btn');
        analysisBtns.forEach(btn => {
            btn.addEventListener('click', async () => {
                const newAnalysisType = btn.getAttribute('data-analysis-type');
                if (newAnalysisType && newAnalysisType !== currentAnalysisType) {
                    await switchAnalysisType(newAnalysisType);
                }
            });
        });

        // Set initial analysis type on the modal body for CSS styling
        const modalBody = overlay.querySelector('.skills-modal-body');
        if (modalBody) {
            modalBody.setAttribute('data-analysis-type', currentAnalysisType);
        }
    }

    async function switchAnalysisType(newType) {
        // Don't switch if it's the same type
        if (newType === currentAnalysisType) return;

        // Update the current type
        const oldType = currentAnalysisType;
        currentAnalysisType = newType;

        // Update active button state
        const buttons = document.querySelectorAll('.skills-analysis-btn');
        buttons.forEach(btn => {
            const btnType = btn.getAttribute('data-analysis-type');
            if (btnType === newType) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // Get the modal body
        const modalBody = document.querySelector('.skills-modal-body');
        if (modalBody) {
            // Add fading out class
            modalBody.classList.add('fading-out');

            // Wait for fade out transition
            await new Promise(resolve => setTimeout(resolve, 300));

            // Update data attribute for CSS
            modalBody.setAttribute('data-analysis-type', newType);

            // Get the active data
            const activeData = getCurrentAnalysisData();

            // Update the competency cards
            const competencyGrid = modalBody.querySelector('.skills-competency-grid');
            if (competencyGrid && activeData?.report?.competencyAnalysis) {
                competencyGrid.innerHTML = Object.entries(activeData.report.competencyAnalysis)
                    .map(([competency, competencyData]) =>
                        createCompetencyCard(competency, competencyData)
                    )
                    .join('');
            }

            // Update summary
            const summaryElement = modalBody.querySelector('.skills-learning-path-section p');
            if (summaryElement && activeData?.report) {
                summaryElement.textContent = activeData.report.summary || '';
            }

            // Update recommendations
            const primaryRecommendationsElement = modalBody.querySelector('.skills-primary-recommendations ul');
            if (primaryRecommendationsElement && activeData?.recommendations) {
                primaryRecommendationsElement.innerHTML = activeData.recommendations.map(rec => `
                    <li>
                        <strong>${rec.course}</strong>
                        <p>${rec.reason}</p>
                    </li>
                `).join('');
            }

            // Update other path recommendations
            const otherPathRecommendationsElement = modalBody.querySelector('#skills-cross-paths-collapsible ul');
            if (otherPathRecommendationsElement && activeData?.other_learning_paths_courses) {
                otherPathRecommendationsElement.innerHTML = activeData.other_learning_paths_courses.map(rec => `
                    <li>
                        <strong>${rec.course}</strong>
                        <p>${rec.reason}</p>
                    </li>
                `).join('');
            }

            // Show/hide other path recommendations section
            const crossPathSection = modalBody.querySelector('.skills-cross-path-recommendations');
            if (crossPathSection) {
                crossPathSection.style.display = (activeData?.other_learning_paths_courses?.length > 0) ? 'block' : 'none';
            }

            // Update enrollment button with more robust handling
            const ctaSection = modalBody.querySelector('.skills-cta-section');
            const shouldShowEnrollButton = activeData &&
                activeData.report &&
                (activeData.report.learningPath || activeData.report.currentPath ||
                 currentData.metadata?.pathsByType?.[newType] || currentData.metadata?.currentPath);

            // If enrollment button should be shown but isn't present, add it
            if (shouldShowEnrollButton) {
                if (!ctaSection) {
                    // Create CTA section if it doesn't exist
                    const newCtaSection = document.createElement('div');
                    newCtaSection.className = 'skills-cta-section';
                    newCtaSection.innerHTML = `
                        <button id="enroll-now-button" class="skills-enroll-now-button">
                            Start Assigning This Training
                        </button>
                    `;
                    modalBody.appendChild(newCtaSection);

                    // Add event listener to the new button - no need to pass a specific path
                    const newEnrollButton = newCtaSection.querySelector('#enroll-now-button');
                    if (newEnrollButton) {
                        newEnrollButton.addEventListener('click', () => handleEnrollment());
                    }
                } else {
                    // Update existing button's event listener
                    const enrollButton = ctaSection.querySelector('#enroll-now-button');
                    if (enrollButton) {
                        // Remove old event listeners by cloning and replacing
                        const newButton = enrollButton.cloneNode(true);
                        enrollButton.parentNode.replaceChild(newButton, enrollButton);

                        // Add new event listener - no need to pass a specific path
                        newButton.addEventListener('click', () => handleEnrollment());
                    }

                    // Make sure the section is visible
                    ctaSection.style.display = 'block';
                }
            } else if (ctaSection) {
                // Hide the enrollment section if there's no relevant data
                ctaSection.style.display = 'none';
            }

            // Update chart with new data
            await createRadarChart(activeData);

            // Remove fading out class and add fading in
            modalBody.classList.remove('fading-out');
            modalBody.classList.add('fading-in');

            // Remove fading in class after transition
            setTimeout(() => {
                modalBody.classList.remove('fading-in');
            }, 300);
        }
    }

    function handleEnrollment() {
        // Get the active analysis type's learning path
        const activeData = getCurrentAnalysisData();

        // Log which analysis type is active for debugging
        console.log('Handling enrollment with active analysis type:', currentAnalysisType);

        // Get the appropriate learning path based on the active analysis type
        let pathToUse = null;

        // First try to get path from active analysis data
        if (activeData?.report?.learningPath) {
            pathToUse = activeData.report.learningPath;
            console.log(`Using ${currentAnalysisType} learning path:`, pathToUse);
        }
        // Then try the type-specific paths from metadata if available
        else if (currentData.metadata?.pathsByType &&
                 currentData.metadata.pathsByType[currentAnalysisType]) {
            pathToUse = currentData.metadata.pathsByType[currentAnalysisType];
            console.log(`Using ${currentAnalysisType} path from metadata:`, pathToUse);
        }
        // Fall back to general currentPath
        else if (currentData.metadata?.currentPath) {
            pathToUse = currentData.metadata.currentPath;
            console.log('Falling back to general currentPath:', pathToUse);
        }

        if (!pathToUse) {
            console.error('No learning path available for enrollment');
            if (typeof showNotification === 'function') {
                showNotification('Unable to determine appropriate learning path for enrollment', 'error');
            }
            return;
        }

        // Hide the modal first
        hideModal();

        // Get the main content element
        const mainContent = document.querySelector('#main-content');
        if (!mainContent) {
            console.error('Main content element not found');
            return;
        }

        // Get user data from current data with robust fallbacks
        const userData = {
            email: activeData?.report?.email || currentData?.metadata?.userId,
            name: activeData?.report?.employeeName || currentData?.metadata?.employeeName,
            company: currentData?.metadata?.userCompany || userCompany
        };

        console.log('Enrollment data:', { learningPath: pathToUse, userData, analysisType: currentAnalysisType });

        // Make sure we have the required data
        if (!userData.email || !userData.company || !pathToUse) {
            console.error('Missing required data for enrollment', { userData, learningPath: pathToUse });
            if (typeof showNotification === 'function') {
                showNotification('Unable to start enrollment process - missing data', 'error');
            }
            return;
        }

        // Convert user data to base64 to safely pass as URL parameter
        const encodedUserData = btoa(JSON.stringify({
            ...userData,
            // Include the analysis type that was used for better tracking
            analysisType: currentAnalysisType
        }));

        // Preserve the previous page in navigation state
        const previousState = navigationState?.stack?.[navigationState?.currentIndex];

        // Load the enrollment page
        if (typeof loadEnrollmentPage === 'function') {
            loadEnrollmentPage(mainContent, pathToUse, {
                highlightUser: encodedUserData,
                previousPage: previousState ? previousState.page : null,
                // Pass the analysis type as an additional parameter
                analysisType: currentAnalysisType
            });
        } else {
            console.error('loadEnrollmentPage function not found');
            if (typeof showNotification === 'function') {
                showNotification('Enrollment functionality is not available', 'error');
            }
        }
    }

    function overlayClickHandler(event) {
        if (event.target.id === 'skills-gap-overlay') {
            hideModal();
        }
    }

    async function hideModal() {
        isClosing = true;
        const overlay = document.getElementById('skills-gap-overlay');
        if (!overlay) return;

        overlay.style.opacity = '0';
        const modalContent = overlay.querySelector('.skills-modal-content');
        if (modalContent) {
            modalContent.style.opacity = '0';
            modalContent.style.transform = 'scale(0.95)';
        }

        // Clean up chart instance
        if (currentChartInstance) {
            currentChartInstance.destroy();
            currentChartInstance = null;
        }

        await new Promise(resolve => setTimeout(resolve, 300));
        overlay.style.display = 'none';
    }

    function showModal() {
        const overlay = document.getElementById('skills-gap-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
            overlay.style.opacity = '0'; // Start with opacity 0

            setTimeout(() => {
                overlay.style.opacity = '1';
                const modalContent = overlay.querySelector('.skills-modal-content');
                if (modalContent) {
                    modalContent.style.opacity = '1';
                    modalContent.style.transform = 'scale(1)';
                }

                // Hide loading overlay after modal is visible
                if (typeof hideLoadingOverlay === 'function') {
                    hideLoadingOverlay();
                }
            }, 50);
        }
    }

    async function exportToPDF() {
        const activeData = getCurrentAnalysisData();

        if (!activeData || !activeData.report) {
            console.error('No data available to export.');
            return;
        }

        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'pt', 'a4');

        // Generate high-resolution chart image
        let chartImage = '';
        try {
            chartImage = await createHighResChartImage(activeData);
        } catch (err) {
            console.error('Error generating high-res chart image:', err);
        }

        const pageWidth = doc.internal.pageSize.getWidth();

        // Title & Header with analysis type
        doc.setFontSize(16);

        // Use appropriate color based on analysis type
        if (currentAnalysisType === 'softSkills') {
            doc.setTextColor(124, 58, 237); // Purple for soft skills
        } else {
            doc.setTextColor(30, 58, 138); // Blue for digital skills
        }

        const analysisTypeText = currentAnalysisType === 'softSkills' ? 'Soft Skills' :
                                 currentAnalysisType === 'digitalSkills' ? 'Digital Skills' :
                                 'Skills';

        doc.text(`${analysisTypeText} Gap Analysis Report`, pageWidth / 2, 50, { align: 'center' });

        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        let employeeName = activeData.report.employeeName || '';
        let role = activeData.report.role ? ` - ${activeData.report.role}` : '';
        doc.text(`${employeeName}${role}`, pageWidth / 2, 70, { align: 'center' });

        // Insert the high-res chart image if available
        if (chartImage) {
            const imageWidth = pageWidth - 80;
            const imageHeight = imageWidth;
            doc.addImage(chartImage, 'PNG', 40, 90, imageWidth, imageHeight);
        }

        let yPos = chartImage ? 90 + (pageWidth - 80) + 30 : 130;

        // Competency Table
        const competencyData = Object.entries(activeData.report.competencyAnalysis).map(([competency, compData]) => {
            return {
                competency: competency,
                proficiency: compData.proficiencyLevel,
                strengths: compData.strengthAreas.join(', '),
                gaps: compData.gapAreas.join(', ')
            };
        });

        doc.setFontSize(12);
        if (currentAnalysisType === 'softSkills') {
            doc.setTextColor(124, 58, 237); // Purple for soft skills
        } else {
            doc.setTextColor(30, 58, 138); // Blue for digital skills
        }
        doc.text('Competency Analysis', 40, yPos);
        yPos += 20;

        // Set table header color based on analysis type
        const headerColor = currentAnalysisType === 'softSkills' ?
            [124, 58, 237] : // Purple for soft skills
            [30, 58, 138];   // Blue for digital skills

        doc.autoTable({
            startY: yPos,
            head: [['Competency', 'Proficiency', 'Strengths', 'Gaps']],
            body: competencyData.map(row => [row.competency, row.proficiency, row.strengths, row.gaps]),
            styles: { fontSize: 10, cellPadding: 5 },
            headStyles: { fillColor: headerColor, textColor: [255, 255, 255] },
            margin: { left: 40, right: 40 }
        });

        yPos = doc.autoTable.previous.finalY + 30;

        // Summary
        doc.setFontSize(12);
        if (currentAnalysisType === 'softSkills') {
            doc.setTextColor(124, 58, 237); // Purple for soft skills
        } else {
            doc.setTextColor(30, 58, 138); // Blue for digital skills
        }
        doc.text('Summary', 40, yPos);
        yPos += 15;

        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);
        const summaryLines = doc.splitTextToSize(activeData.report.summary || '', pageWidth - 80);
        doc.text(summaryLines, 40, yPos);
        yPos += (summaryLines.length * 12) + 20;

        // Primary Recommendations
        if (activeData.recommendations && activeData.recommendations.length > 0) {
            doc.setFontSize(12);
            if (currentAnalysisType === 'softSkills') {
                doc.setTextColor(124, 58, 237); // Purple for soft skills
            } else {
                doc.setTextColor(30, 58, 138); // Blue for digital skills
            }
            doc.text('Current Learning Path Recommendations', 40, yPos);
            yPos += 20;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            for (const rec of activeData.recommendations) {
                // Check if we're close to the bottom of the page
                if (yPos > doc.internal.pageSize.getHeight() - 100) {
                    doc.addPage();
                    yPos = 50; // Reset Y position on new page
                }

                doc.setFont(undefined, 'bold');
                doc.text(`${rec.course}`, 40, yPos);
                yPos += 15;

                doc.setFont(undefined, 'normal');
                const wrappedReason = doc.splitTextToSize(rec.reason, pageWidth - 80);
                doc.text(wrappedReason, 40, yPos);
                yPos += (wrappedReason.length * 12) + 20;
            }
        }

        // Cross-Path Recommendations
        if (activeData.other_learning_paths_courses && activeData.other_learning_paths_courses.length > 0) {
            // Check if we need to add a new page
            if (yPos > doc.internal.pageSize.getHeight() - 100) {
                doc.addPage();
                yPos = 50;
            }

            doc.setFontSize(12);
            if (currentAnalysisType === 'softSkills') {
                doc.setTextColor(124, 58, 237); // Purple for soft skills
            } else {
                doc.setTextColor(30, 58, 138); // Blue for digital skills
            }
            doc.text('Additional Learning Path Recommendations', 40, yPos);
            yPos += 20;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            for (const rec of activeData.other_learning_paths_courses) {
                // Check if we're close to the bottom of the page
                if (yPos > doc.internal.pageSize.getHeight() - 100) {
                    doc.addPage();
                    yPos = 50; // Reset Y position on new page
                }

                doc.setFont(undefined, 'bold');
                doc.text(`${rec.course}`, 40, yPos);
                yPos += 15;

                // Add the learning path info if available
                if (rec.learningPath) {
                    doc.setFont(undefined, 'italic');
                    doc.text(`Path: ${rec.learningPath}`, 40, yPos);
                    yPos += 15;
                }

                doc.setFont(undefined, 'normal');
                const wrappedReason = doc.splitTextToSize(rec.reason, pageWidth - 80);
                doc.text(wrappedReason, 40, yPos);
                yPos += (wrappedReason.length * 12) + 20;
            }
        }

        // Add footer with generated date
        const now = new Date();
        const dateStr = now.toLocaleDateString();
        doc.setFontSize(8);
        doc.setTextColor(128, 128, 128);
        doc.text(`Generated on ${dateStr}`, pageWidth - 40, doc.internal.pageSize.getHeight() - 20, { align: 'right' });

        doc.save(`${analysisTypeText.replace(/\s+/g, '_')}_Gap_Analysis_Report.pdf`);
    }

    // Public API
    global.showSkillsGapAnalysis = showSkillsGapAnalysis;
})(typeof window !== 'undefined' ? window : global);